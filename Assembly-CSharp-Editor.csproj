<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{1ff767a6-373d-9687-1a66-96e3fbb25b62}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-Editor</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Unity.Rider.Editor\</OutputPath>
    <DefineConstants>UNITY_EDITOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\differences.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm-help.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\semantic.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\basecommands.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\binmergetool.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\commontypes.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\guihelp.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\workspaceserver.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\i3.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\commontypes.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\xdiff.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\i3.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\differences.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\common.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\images.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\basecommands.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\xdiff.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\mergetool.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\i3.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\guihelp.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\basecommands.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\plastic-gui.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm-help.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\mergetool.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\guihelp.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\configurehelper.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\binmergetool.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\basecommands.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\images.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\guihelp.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\mergetool.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\clientcommon.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\xdiff.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\i3.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\workspaceserver.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\plastic-gui.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\semantic.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\plastic-gui.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\configurehelper.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\binmergetool.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\binmergetool.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\plastic-gui.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\workspaceserver.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\guihelp.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\binmergetool.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\images.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\binmergetool.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\clientcommon.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\commontypes.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\semantic.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\basecommands.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\differences.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm-help.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm-help.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\images.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\i3.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\commontypes.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\configurehelper.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\workspaceserver.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\mergetool.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\clientcommon.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\FileSystemWatcherLicense.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\commontypes.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\clientcommon.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\basecommands.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\xdiff.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\differences.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\images.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\mergetool.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\differences.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\configurehelper.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\semantic.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\mergetool.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\workspaceserver.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\i3.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\images.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\semantic.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\plastic-gui.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\xdiff.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\common.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\clientcommon.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\guihelp.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\configurehelper.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\clientcommon.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\semantic.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\configurehelper.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm-help.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\workspaceserver.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm-help.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\plastic-gui.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\commontypes.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\xdiff.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\cm.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Localization\differences.en.txt" />
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
