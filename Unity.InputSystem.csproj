<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{aff71b22-57a1-a64b-0d6b-9feb67a1e080}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Unity.InputSystem</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Unity.InputSystem\</OutputPath>
    <DefineConstants>UNITY_6000_0_47;UNITY_6000_0;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AUDIO;ENABLE_CLOTH;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_WEBGL;UNITY_WEBGL;UNITY_WEBGL_API;UNITY_DISABLE_WEB_VERIFICATION;UNITY_GFX_USE_PLATFORM_VSYNC;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;UNITY_INPUT_SYSTEM_ENABLE_VR;UNITY_INPUT_SYSTEM_ENABLE_XR;UNITY_INPUT_SYSTEM_ENABLE_PHYSICS;UNITY_INPUT_SYSTEM_ENABLE_PHYSICS2D;UNITY_INPUT_SYSTEM_ENABLE_UI;HAS_SET_LOCAL_POSITION_AND_ROTATION;UNITY_INPUT_SYSTEM_PROJECT_WIDE_ACTIONS;UNITY_INPUT_SYSTEM_INPUT_ACTIONS_EDITOR_AUTO_SAVE_ON_FOCUS_LOST;UNITY_INPUT_SYSTEM_PLATFORM_SCROLL_DELTA;UNITY_INPUT_SYSTEM_INPUT_MODULE_SCROLL_DELTA;UNITY_INPUT_SYSTEM_SENDPOINTERHOVERTOPARENT;UNITY_INPUT_SYSTEM_ENABLE_ANALYTICS;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\DisableDeviceCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\TouchControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryEnabledStateCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Android\AndroidAxis.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\PlayerInputEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\MultiplayerEventSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\QuaternionControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\HID\HIDParser.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\InputStateWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\Selectors.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\EditorWindowSpaceProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\DualShock\DualShockGamepadHID.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\InitiateUserAccountPairingCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\iOS\iOSSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Interactions\MultiTapInteraction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Switch\SwitchSupportHID.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\MiscHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\IViewStateCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Steam\SteamController.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\AnyKeyControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\State\InputStateBlock.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\InvertProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Linux\LinuxSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\InputEventBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\IInputControlPickerLayout.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\InputSystemPluginControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\ExpressionUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\InputActionPropertiesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\CommonUsages.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Settings\InputSettingsBuildProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControlLayoutChange.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\SetIMECursorPositionCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\InputActionsEditorState.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionSetupExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\TrackedDevice.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\ButtonControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\InputActionSerializationHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Haptics\SendHapticImpulseCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Composites\AxisComposite.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\EnhancedTouch\Touch.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\DiscreteButtonControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\ExceptionHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\CompositeBindingPropertiesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ProjectWideActions\ProjectWideActionsBuildProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetImporter\InputActionImporterEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Commands\ControlSchemeCommands.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Users\InputUserPairingOptions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\InputDeviceBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\NormalizeVector3Processor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Gamepad.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Vector3Control.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\UISupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Interactions\HoldInteraction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\NavigationModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\BuildPipeline\LinkFileGenerator.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\KeyControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Settings\EditorPlayerSettingHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\ActionEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\NameAndParameters.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\ControlSchemesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\GenericXRDevice.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\EnhancedTouch\EnhancedTouchSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionState.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Precompiled\FastTouchscreen.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetImporter\InputActionAssetEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Composites\TwoModifiersComposite.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\StickControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\InputActionsTreeViewItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Touchscreen.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\PlayerInputManagerEditorAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\DeviceConfigurationEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputUpdateType.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\DpadControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\InputActionTreeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XInput\XboxGamepadMacOS.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ProjectWideActions\ProjectWideActionsAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\InputDeviceCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Observables\TakeNObservable.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\InputControlPicker.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\OnScreen\OnScreenSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Substring.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputBindingResolver.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\NormalizeProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\InputExitPlayModeAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\DeviceResetEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\InternedString.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\State\IInputStateCallbackReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\Layouts\TouchscreenControlPickerLayout.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\iOS\iOSStepCounter.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Debugger\SampleFrequencyCalculator.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\IMECompositionEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\StandaloneInputModuleEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Pen.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Devices\OpenVR.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\iOS\IOSGameController.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\VisualElementExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\DeltaStateEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XInput\IXboxOneRumble.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\InputComponentEditorAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\EnhancedTouch\TouchHistory.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\MatchingControlPaths.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Precompiled\FastKeyboard.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\ScaleProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\ReactiveProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\IInputDeviceCommandInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\RequestSyncCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\DeviceRemoveEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Keyboard.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Devices\GoogleVR.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\ClampProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Devices\WindowsMR.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControlLayout.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Steam\SteamSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControlList.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\JsonParser.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\HID\HIDDescriptorWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\AxisDeadzoneProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Steam\SteamControllerType.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Users\InputUserSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\SerializedInputAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Observables\Observer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\InputActionsEditorWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryEditorWindowCoordinatesCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\IEventMerger.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Android\AndroidSensors.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Precompiled\FastMouse.partial.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\INavigationEventData.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\NameAndParameterListView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryUserIdCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\InputValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\DualShock\DualShockSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Observables\WhereObservable.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\InputBuildAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Composites\ButtonWithOneModifier.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\AdvancedDropdownState.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\ActionPropertiesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\TrackedPoseDriver.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UnityRemote\UnityRemoteSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionRebindingExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\InputAssetEditorUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryKeyNameCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\OnScreen\OnScreenStick.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\BindingPropertiesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionParameters.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Users\InputUser.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\XRSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\AdvancedDropdownDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\InputParameterEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\WebGL\WebGLSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\InputBindingPropertiesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\EditorHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\InputEventTrace.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\ExtendedAxisEventData.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\PredictiveParser.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\InputActionEditorWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputMetrics.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Observables\ForDeviceEventObservable.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\SavedState.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\OneOrMore.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputControlScheme.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionType.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\DelegateHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\ViewStateCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\BaseInputOverride.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\InputLayoutCodeGenerator.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\RequestResetCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\PropertiesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\IInputDiagnostics.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Users\InputUserAccountHandle.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\TypeTable.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\PropertiesViewBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControlAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Haptics\IHaptics.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\AdvancedDropdown.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\StringHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\SerializedPropertyHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\ScaleVector3Processor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControlExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Interactions\SlowTapInteraction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\NormalizeVector2Processor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Remote\InputRemoting.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Dialog.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\PrimitiveValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\InputControlDropdownItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\InputDiagnostics.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryKeyboardLayoutCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Composites\Vector2Composite.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Debugger\InputLatencyCalculator.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\EnableIMECompositionCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\IInputRuntime.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Haptics\BufferedRumble.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\PlayerInputManagerEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\StickDeadzoneProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputBinding.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\PlayerInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\State\InputStateBuffers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionMapDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Haptics\GetHapticCapabilitiesCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputSystemObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\ActionsTreeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\EnableDeviceCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\WebGL\WebGLJoystick.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionPhase.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\SerializedInputActionMap.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\ContextMenu.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\GUIHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Mouse.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Switch\SwitchProControllerHID.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\OnScreen\OnScreenControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\InputEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\InputActionAssetManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetImporter\IInputActionAssetEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Devices\Oculus.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\DoubleControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\ScaleVector2Processor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Remote\RemoteInputPlayerConnection.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\InputEventStream.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\InputEventTreeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Comparers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\SerializedPropertyLinqExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\State\IInputStateTypeInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Interactions\PressInteraction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\DynamicBitfield.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\StateContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Composites\OneModifierComposite.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionReferenceSearchProviders.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\DisplayStringFormatAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\CompensateRotationProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Settings\InputSettingsProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\State\InputState.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Vector2Control.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Pointer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\EnumerableExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\PlayerJoinBehavior.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Commands\Commands.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Linux\SDLDeviceBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Haptics\SendBufferedHapticsCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\ICustomDeviceReset.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\WarpMousePositionCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\InputSystemPackageControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\MultiLevelDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\InputActionEditorToolbar.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\CopyPasteHelper.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionReference.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\AdvancedDropdownWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Debugger\InputDebuggerWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\PlayerInputManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\AdvancedDropdownItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\DropManipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\NativeInputRuntime.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\SpriteUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\CollectionViewSelectionChangeFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\SerializedInputBinding.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetImporter\InputActionCodeGenerator.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\BuildProviderHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\OnScreenStickEditorAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetImporter\InputActionAssetIconLoader.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\iOS\iOSPostProcessBuild.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\HID\HID.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Interactions\TapInteraction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputBindingComposite.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\FourCC.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\DownloadableSample.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\OnScreen\OnScreenButton.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\IntegerControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Android\AndroidGameController.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\InputActionsEditorConstants.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputManagerStateMonitors.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\InputSystemUIInputModuleEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\iOS\InputSettingsiOSProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\DualShock\IDualShockHaptics.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\PlayerNotifications.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputFeatureNames.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\DeviceSimulator\InputSystemPlugin.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\InvertVector3Processor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Steam\SteamIGAConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\InputDeviceChange.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputControlPathDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\AdvancedDropdownGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XInput\XInputController.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\TrackedDeviceRaycaster.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Debugger\InputDeviceDebuggerWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionReferencePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\CSharpCodeHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\ViewBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\VirtualMouseInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Settings\InputEditorUserSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\InputDeviceMatcher.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\CompensateDirectionProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\IEventPreProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\State\InputStateHistory.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionDrawerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\ReadOnlyArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\TypeHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\Processors\InvertVector2Processor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\iOS\InputSettingsiOS.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\InputEventListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\ITextInputReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\AdvancedDropdown\CallbackDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\InputSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\MemoryHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XInput\XInputSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Android\AndroidKeyCode.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryCanRunInBackground.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\IInputActionCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Joystick.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Steam\IStreamControllerAPI.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\PlayerInput\DefaultInputActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QuerySamplingFrequencyCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\InputActionTreeViewItems.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\HID\HIDSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionChange.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Debugger\InputActionDebuggerWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\StateEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\InlinedArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\InputActionMapsTreeViewItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\OSX\OSXGameController.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\AxisControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\ExtendedPointerEventData.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryPairedUserAccountCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputInteractionContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\Layouts\DefaultInputControlPickerLayout.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XInput\XInputControllerWindows.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\CallbackArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\InputActionsEditorWindowUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionAssetDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Observables\Observable.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControlLayoutAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\NameAndParametersListView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\State\IInputStateChangeMonitor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Precompiled\FastMouse.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\CompositePartBindingPropertiesView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\ArrayHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\DeltaControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\ExtendedSubmitCancelEventData.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Sensor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Haptics\DualMotorRumbleCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\InputControlPathEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\TextEvent.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetImporter\InputActionImporter.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\ActionMapsView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\EnhancedTouch\TouchSimulation.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\EditorInputControlLayoutCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\InputControlPickerDropdown.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\IInputInteraction.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Users\InputUserChange.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\InputSystemUIInputModule.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Haptics\DualMotorRumble.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionMap.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputBindingCompositeContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\InputControlTreeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\TouchPressControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\NumberHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\VirtualMouseInputEditorAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\GamepadButtonPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Observables\SelectObservable.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\InputDevice.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\WebGL\WebGLGamepad.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\IInputEventTypeInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\InputActionTrace.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\TouchPhaseControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Composites\ButtonWithTwoModifiers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\PlayerInputEditorAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\EnhancedTouch\Finger.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Haptics\IDualMotorRumble.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Internal\TreeViewHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\QueryDimensionsCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\XRLayoutBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\AssetEditor\ParameterListView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\UI\PointerModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\IInputUpdateCallbackReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\InputEditorAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Android\AndroidSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\Commands\SetSamplingFrequencyCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\PropertyDrawers\InputActionAssetSearchProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Actions\Composites\Vector3Composite.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\Steam\SteamHandle.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Controls\PoseControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Controls\InputControlPath.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\Views\InputActionsEditorView.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Events\InputEventPtr.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\NamedValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\OSX\OSXSupport.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\InputActionsEditorSettingsProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\XR\Haptics\GetCurrentHapticStateCommand.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ControlPicker\InputControlPickerState.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Utilities\Observables\SelectManyObservable.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Devices\InputDeviceDescription.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\Analytics\InputActionsEditorSessionAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Plugins\DualShock\DualShockGamepad.cs" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\ControlSchemeEditor.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\BindingPanelRowTemplate.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\InputActionsTreeViewItem.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\InputActionsProjectSettings.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Unity.InputSystem.asmdef" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\InputActionsEditor.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\ProjectWideActions\ProjectWideActionsTemplate.json" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\CompositeBindingPropertiesEditor.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\NameAndParameterListViewItemTemplate.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\CompositePartBindingPropertiesEditor.uxml" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\InputAssetEditorDark.uss" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\InputActionsEditorStyles.uss" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\InputAssetEditorLight.uss" />
    <None Include="Library\PackageCache\com.unity.inputsystem@7fe8299111a7\InputSystem\Editor\UITKAssetEditor\PackageResources\InputActionMapsTreeViewItem.uxml" />
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WebGLModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\PlaybackEngines\WebGLSupport\Managed\UnityEngine.WebGLModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.visualscripting@7dcdc439b230\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>C:\Users\<USER>\Workspaces\war-cards\Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
