using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(SpriteRenderer))]
public class Card : MonoBehaviour
{
    private CardData _data;
    private SpriteRenderer _renderer;
    private Collider2D _collider;
    private Vector3 _startDragPosition;

    private readonly List<Collider2D> _overlapResults = new();
    private readonly ContactFilter2D _filter = new ContactFilter2D().NoFilter();

    private void Awake()
    {
        _collider = GetComponent<Collider2D>();
    }

    public void Init(CardData data)
    {
        name = data.name;
        _renderer = GetComponent<SpriteRenderer>();
        _data = data;

        _renderer.sprite = _data.Sprite;
    }

    public void SetDrawingOrder(int drawingOrder)
    {
        _renderer.sortingOrder = drawingOrder;
    }

    public void OnMouseDown()
    {
        _startDragPosition = transform.position;
        transform.position = GetMousePositionInWorldSpace();
    }

    public void OnMouseDrag()
    {
        transform.position = GetMousePositionInWorldSpace();
    }

    public void OnMouseUp()
    {
        // Get all colliders overlapping this card's bounds
        var hitCount = Physics2D.OverlapBox(
            _collider.bounds.center,
            _collider.bounds.size,
            0f,
            _filter,
            _overlapResults
        );

        // Look for any drop area in those overlaps
        for (int i = 0; i < hitCount; i++)
        {
            Collider2D hit = _overlapResults[i];

            if (hit == _collider)
                continue;

            if (!hit.TryGetComponent(out ICardDropArea dropArea))
                continue;

            Debug.Log($"Dropped {name} on {dropArea.GetType().Name}");
            dropArea.OnCardDrop(this);
            return;
        }

        transform.position = _startDragPosition;
    }

    private Vector3 GetMousePositionInWorldSpace()
    {
        var p = Camera.main.ScreenToWorldPoint(Input.mousePosition);
        p.z = 0;
        return p;
    }
}