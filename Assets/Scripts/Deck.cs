using System.Collections.Generic;
using PrimeTween;
using UnityEngine;
using UnityEngine.Splines;

public class Deck : Singleton<Deck>
{
    [SerializeField] private CardCollection _cardCollection;
    [SerializeField] private Card _cardPrefab;
    [SerializeField] private SplineContainer _splineContainer;
    [SerializeField] private int _maxHandSize = 2;

    [<PERSON><PERSON>("<PERSON><PERSON>")]
    [SerializeField] private List<Card> _deckPile = new();
    [SerializeField] private List<Card> _handPile;
    [SerializeField] private List<Card> _discardPile;

    private void Start()
    {
        Init();
    }

    [ContextMenu("Init")]
    public void Init()
    {
        var cards = _cardCollection.Cards;

        foreach (var cardData in cards)
        {
            var card = Instantiate(_cardPrefab, transform.position, Quaternion.identity);
            card.gameObject.SetActive(false);
            card.Init(cardData);
            _deckPile.Add(card);
        }

        _deckPile.Shuffle();
    }

    [ContextMenu("Shuffle")]
    public void Shuffle()
    {
        _deckPile.Shuffle();
        Debug.Log("Shuffled");
    }

    public bool Draw(out Card card)
    {
        if (_deckPile.Count == 0)
        {
            card = null;
            return false;
        }

        var c = _deckPile.Pop();
        c.gameObject.SetActive(true);

        Debug.Log($"Drawn {c.name}");

        card = c;
        return true;
    }

    public void Discard(Card card)
    {
        if (_handPile.Contains(card) == false)
            return;
    
        _handPile.Remove(card);
        _discardPile.Add(card);
    
        card.gameObject.SetActive(false);
        card.transform.localScale = transform.position;
        
        UpdateCardsPosition();
    
        Debug.Log($"Discarded {card.name}");
    }

    // public void ReturnToDeck(Card card, bool shuffle = true)
    // {
    //     if (!_handPile.Contains(card))
    //         return;
    //
    //     card.gameObject.SetActive(false);
    //     _handPile.Remove(card);
    //     _deckPile.Add(card);
    //
    //     if (shuffle)
    //         _deckPile.Shuffle();
    //
    //     Debug.Log($"Returned {card.name} to deck");
    // }

    [ContextMenu("Draw")]
    public void Draw()
    {
        if (_handPile.Count >= _maxHandSize)
            return;

        if (!Draw(out var card))
            return;

        _handPile.Add(card);
        UpdateCardsPosition();
    }

    private void UpdateCardsPosition()
    {
        float cardSpacing = 1f / _maxHandSize;
        float firstCardPosition = .5f - (_handPile.Count - 1) * cardSpacing / 2;
        Spline spline = _splineContainer.Spline;

        for (int i = 0; i < _handPile.Count; i++)
        {
            var card = _handPile[i];
            float p = firstCardPosition + i * cardSpacing;
            
            Vector3 splinePosition = spline.EvaluatePosition(p);
            splinePosition.z = 0;
                
            var forward = spline.EvaluateTangent(p);
            var up = spline.EvaluateUpVector(p);
            var rotation = Quaternion.LookRotation(-up, -Vector3.Cross(up, forward).normalized);

            card.SetDrawingOrder(i);
            Tween.Position(card.transform, splinePosition, 0.25f);
            Tween.Rotation(card.transform, rotation, 0.25f);
        }
    }
}