.unity-imgui-container {
    flex: 0 0 0;
}
.bottomRight {
    bottom: 16px;
    right: 16px;
}
.bottomLeft {
    bottom: 16px;
    left: 0;
}
.topLeft {
    top: 0;
    left: 0;
}
.topRight {
    top: 0;
    right: 16px;
}
.bottomRightFloating {
    bottom: 24px;
    right: 24px;
}
.bottomLeftFloating {
    bottom: 24px;
    left: 8px;
}
.topLeftFloating {
    top: 8px;
    left: 8px;
}
.topRightFloating {
    top: 8px;
    right: 24px;
}

.unity-grid-paint-palette-window {
    background-color: var(--unity-colors-tab-background-checked);
}

.unity-tilepalette-activetargets {
    min-height: 30px;
    align-items: center;
    justify-content: center;
}

.unity-tilepalette-toolbar {
    margin-top: 4px;
    margin-bottom: 1px;
    align-items: center;
    justify-content: center;
}

.unity-toolbar-button {
    background-color: var(--unity-colors-button-background);
    border-width: 0;
    min-height: var(--toolbar-button-height);
    left: 0;
}
.unity-toolbar-button:hover {
    background-color: var(--unity-colors-button-background-hover);
}
.unity-toolbar-button:checked {
    background-color: var(--unity-overlay-buttons-on-color);
}

.unity-toolbar-button:active {
    background-color: var(--unity-colors-button-background-pressed);
}

.unity-toolbar-toggle:checked {
    background-color: var(--unity-overlay-buttons-on-color);
}

.unity-editor-toolbar__button-strip-element {
    left: 0;
    margin-right: 1px;
}

.unity-tilepalette-splitview .unity-tilepalette-element .unity-tilepalette-element-brushelement {
    visibility: visible;
    position: relative;
}
