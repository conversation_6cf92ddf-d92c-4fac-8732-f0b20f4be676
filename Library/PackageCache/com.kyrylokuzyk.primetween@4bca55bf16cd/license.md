It's **allowed**:
- Use PrimeTween and modify its source code in **free and commercial** products distributed in binary format (apps and games).
- Use PrimeTween in derivative (free and commercial) projects (templates, libraries, GitHub projects) that depend on PrimeTween, but only if PrimeTween is installed as an [**NPM package**](https://github.com/KyryloKuzyk/PrimeTween#install-via-unity-package-manager-upm).

It's **not allowed**:
- Distribute PrimeTween's source code and tarball (.tgz) archive inside derivative products. To build templates or libraries that depend on PrimeTween, use the NPM installation method instead.
- Distribute, resell, or claim PrimeTween's ownership even if modified.
- Resell PrimeTween by itself or as part of another library or asset pack.

**In short**: you can use PrimeTween in your Unity projects, including commercial ones, but you can’t repackage or resell PrimeTween itself.

Copyright: Kyrylo Kuzyk 2023