// This file is generated by CodeGenerator.cs
// ReSharper disable PossibleNullReferenceException
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable UnusedMember.Global
// ReSharper disable UnusedMember.Local
// ReSharper disable UnusedMethodReturnValue.Global
using System;
using JetBrains.Annotations;

namespace PrimeTween {
    internal enum TweenType {
        None,
        MainSequence,
        NestedSequence,
        TweenComponent,

        Delay,
        Callback,

        ShakeLocalPosition,
        ShakeLocalRotation,
        ShakeScale,
        ShakeCustom,
        ShakeCamera,

        CustomFloat,
        CustomColor,
        CustomVector2,
        CustomVector3,
        CustomVector4,
        CustomQuaternion,
        CustomRect,
        CustomDouble,

        MaterialColorProperty,
        MaterialProperty,
        MaterialAlphaProperty,
        MaterialTextureOffset,
        MaterialTextureScale,
        MaterialPropertyVector4,

        EulerAngles,
        LocalEulerAngles,
        GlobalTimeScale,

        // CODE GENERATOR BEGIN
        LightRange,
        LightShadowStrength,
        LightIntensity,
        LightColor,
        CameraOrthographicSize,
        CameraBackgroundColor,
        CameraAspect,
        CameraFarClipPlane,
        CameraFieldOfView,
        CameraNearClipPlane,
        CameraPixelRect,
        CameraRect,
        LocalRotation,
        ScaleUniform,
        Rotation,
        Position,
        PositionX,
        PositionY,
        PositionZ,
        LocalPosition,
        LocalPositionX,
        LocalPositionY,
        LocalPositionZ,
        RotationQuaternion,
        LocalRotationQuaternion,
        Scale,
        ScaleX,
        ScaleY,
        ScaleZ,
        Color,
        Alpha,
        TweenTimeScale,
        TweenTimeScaleSequence,
        UISliderValue,
        UINormalizedPosition,
        UIHorizontalNormalizedPosition,
        UIVerticalNormalizedPosition,
        UIPivotX,
        UIPivotY,
        UIPivot,
        UIAnchorMax,
        UIAnchorMin,
        UIAnchoredPosition3D,
        UIAnchoredPosition3DX,
        UIAnchoredPosition3DY,
        UIAnchoredPosition3DZ,
        UIEffectDistance,
        UIAlphaShadow,
        UIColorShadow,
        UIPreferredSize,
        UIPreferredWidth,
        UIPreferredHeight,
        UIFlexibleSize,
        UIFlexibleWidth,
        UIFlexibleHeight,
        UIMinSize,
        UIMinWidth,
        UIMinHeight,
        UIColorGraphic,
        UIAnchoredPosition,
        UIAnchoredPositionX,
        UIAnchoredPositionY,
        UISizeDelta,
        UIAlphaCanvasGroup,
        UIAlphaGraphic,
        UIFillAmount,
        UIOffsetMin,
        UIOffsetMinX,
        UIOffsetMinY,
        UIOffsetMax,
        UIOffsetMaxX,
        UIOffsetMaxY,
        RigidbodyMovePosition,
        RigidbodyMoveRotation,
        RigidbodyMovePosition2D,
        RigidbodyMoveRotation2D,
        MaterialColor,
        MaterialAlpha,
        MaterialMainTextureOffset,
        MaterialMainTextureScale,
        AudioVolume,
        AudioPitch,
        AudioPanStereo,
        VisualElementLayout,
        VisualElementPosition,
        VisualElementRotationQuaternion,
        VisualElementScale,
        VisualElementSize,
        VisualElementTopLeft,
        VisualElementColor,
        VisualElementBackgroundColor,
        VisualElementOpacity,
        TextMaxVisibleCharacters,
        TextFontSize,
    }

    public partial struct Tween {
        public static Tween LightRange([NotNull] UnityEngine.Light target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightRange(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightRange([NotNull] UnityEngine.Light target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightRange(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightRange([NotNull] UnityEngine.Light target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightRange(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightRange([NotNull] UnityEngine.Light target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightRange(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightRange([NotNull] UnityEngine.Light target, Single endValue, TweenSettings settings) => LightRange(target, new TweenSettings<float>(endValue, settings));
        public static Tween LightRange([NotNull] UnityEngine.Light target, Single startValue, Single endValue, TweenSettings settings) => LightRange(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween LightRange([NotNull] UnityEngine.Light target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Light;
                var val = _tween.FloatVal;
                _target.range = val;
            }, t => (t.target as UnityEngine.Light).range.ToContainer(), TweenType.LightRange);
        }

        public static Tween LightShadowStrength([NotNull] UnityEngine.Light target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightShadowStrength(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightShadowStrength([NotNull] UnityEngine.Light target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightShadowStrength(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightShadowStrength([NotNull] UnityEngine.Light target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightShadowStrength(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightShadowStrength([NotNull] UnityEngine.Light target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightShadowStrength(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightShadowStrength([NotNull] UnityEngine.Light target, Single endValue, TweenSettings settings) => LightShadowStrength(target, new TweenSettings<float>(endValue, settings));
        public static Tween LightShadowStrength([NotNull] UnityEngine.Light target, Single startValue, Single endValue, TweenSettings settings) => LightShadowStrength(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween LightShadowStrength([NotNull] UnityEngine.Light target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Light;
                var val = _tween.FloatVal;
                _target.shadowStrength = val;
            }, t => (t.target as UnityEngine.Light).shadowStrength.ToContainer(), TweenType.LightShadowStrength);
        }

        public static Tween LightIntensity([NotNull] UnityEngine.Light target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightIntensity(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightIntensity([NotNull] UnityEngine.Light target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightIntensity(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightIntensity([NotNull] UnityEngine.Light target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightIntensity(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightIntensity([NotNull] UnityEngine.Light target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightIntensity(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightIntensity([NotNull] UnityEngine.Light target, Single endValue, TweenSettings settings) => LightIntensity(target, new TweenSettings<float>(endValue, settings));
        public static Tween LightIntensity([NotNull] UnityEngine.Light target, Single startValue, Single endValue, TweenSettings settings) => LightIntensity(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween LightIntensity([NotNull] UnityEngine.Light target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Light;
                var val = _tween.FloatVal;
                _target.intensity = val;
            }, t => (t.target as UnityEngine.Light).intensity.ToContainer(), TweenType.LightIntensity);
        }

        public static Tween LightColor([NotNull] UnityEngine.Light target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightColor([NotNull] UnityEngine.Light target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightColor([NotNull] UnityEngine.Light target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightColor([NotNull] UnityEngine.Light target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LightColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LightColor([NotNull] UnityEngine.Light target, UnityEngine.Color endValue, TweenSettings settings) => LightColor(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween LightColor([NotNull] UnityEngine.Light target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => LightColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween LightColor([NotNull] UnityEngine.Light target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Light;
                var val = _tween.ColorVal;
                _target.color = val;
            }, t => (t.target as UnityEngine.Light).color.ToContainer(), TweenType.LightColor);
        }

        public static Tween CameraOrthographicSize([NotNull] UnityEngine.Camera target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraOrthographicSize(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraOrthographicSize([NotNull] UnityEngine.Camera target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraOrthographicSize(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraOrthographicSize([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraOrthographicSize(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraOrthographicSize([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraOrthographicSize(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraOrthographicSize([NotNull] UnityEngine.Camera target, Single endValue, TweenSettings settings) => CameraOrthographicSize(target, new TweenSettings<float>(endValue, settings));
        public static Tween CameraOrthographicSize([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, TweenSettings settings) => CameraOrthographicSize(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween CameraOrthographicSize([NotNull] UnityEngine.Camera target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.FloatVal;
                _target.orthographicSize = val;
            }, t => (t.target as UnityEngine.Camera).orthographicSize.ToContainer(), TweenType.CameraOrthographicSize);
        }

        public static Tween CameraBackgroundColor([NotNull] UnityEngine.Camera target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraBackgroundColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraBackgroundColor([NotNull] UnityEngine.Camera target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraBackgroundColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraBackgroundColor([NotNull] UnityEngine.Camera target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraBackgroundColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraBackgroundColor([NotNull] UnityEngine.Camera target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraBackgroundColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraBackgroundColor([NotNull] UnityEngine.Camera target, UnityEngine.Color endValue, TweenSettings settings) => CameraBackgroundColor(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween CameraBackgroundColor([NotNull] UnityEngine.Camera target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => CameraBackgroundColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween CameraBackgroundColor([NotNull] UnityEngine.Camera target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.ColorVal;
                _target.backgroundColor = val;
            }, t => (t.target as UnityEngine.Camera).backgroundColor.ToContainer(), TweenType.CameraBackgroundColor);
        }

        public static Tween CameraAspect([NotNull] UnityEngine.Camera target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraAspect(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraAspect([NotNull] UnityEngine.Camera target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraAspect(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraAspect([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraAspect(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraAspect([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraAspect(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraAspect([NotNull] UnityEngine.Camera target, Single endValue, TweenSettings settings) => CameraAspect(target, new TweenSettings<float>(endValue, settings));
        public static Tween CameraAspect([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, TweenSettings settings) => CameraAspect(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween CameraAspect([NotNull] UnityEngine.Camera target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.FloatVal;
                _target.aspect = val;
            }, t => (t.target as UnityEngine.Camera).aspect.ToContainer(), TweenType.CameraAspect);
        }

        public static Tween CameraFarClipPlane([NotNull] UnityEngine.Camera target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFarClipPlane(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFarClipPlane([NotNull] UnityEngine.Camera target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFarClipPlane(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFarClipPlane([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFarClipPlane(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFarClipPlane([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFarClipPlane(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFarClipPlane([NotNull] UnityEngine.Camera target, Single endValue, TweenSettings settings) => CameraFarClipPlane(target, new TweenSettings<float>(endValue, settings));
        public static Tween CameraFarClipPlane([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, TweenSettings settings) => CameraFarClipPlane(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween CameraFarClipPlane([NotNull] UnityEngine.Camera target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.FloatVal;
                _target.farClipPlane = val;
            }, t => (t.target as UnityEngine.Camera).farClipPlane.ToContainer(), TweenType.CameraFarClipPlane);
        }

        public static Tween CameraFieldOfView([NotNull] UnityEngine.Camera target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFieldOfView(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFieldOfView([NotNull] UnityEngine.Camera target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFieldOfView(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFieldOfView([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFieldOfView(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFieldOfView([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraFieldOfView(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraFieldOfView([NotNull] UnityEngine.Camera target, Single endValue, TweenSettings settings) => CameraFieldOfView(target, new TweenSettings<float>(endValue, settings));
        public static Tween CameraFieldOfView([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, TweenSettings settings) => CameraFieldOfView(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween CameraFieldOfView([NotNull] UnityEngine.Camera target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.FloatVal;
                _target.fieldOfView = val;
            }, t => (t.target as UnityEngine.Camera).fieldOfView.ToContainer(), TweenType.CameraFieldOfView);
        }

        public static Tween CameraNearClipPlane([NotNull] UnityEngine.Camera target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraNearClipPlane(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraNearClipPlane([NotNull] UnityEngine.Camera target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraNearClipPlane(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraNearClipPlane([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraNearClipPlane(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraNearClipPlane([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraNearClipPlane(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraNearClipPlane([NotNull] UnityEngine.Camera target, Single endValue, TweenSettings settings) => CameraNearClipPlane(target, new TweenSettings<float>(endValue, settings));
        public static Tween CameraNearClipPlane([NotNull] UnityEngine.Camera target, Single startValue, Single endValue, TweenSettings settings) => CameraNearClipPlane(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween CameraNearClipPlane([NotNull] UnityEngine.Camera target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.FloatVal;
                _target.nearClipPlane = val;
            }, t => (t.target as UnityEngine.Camera).nearClipPlane.ToContainer(), TweenType.CameraNearClipPlane);
        }

        public static Tween CameraPixelRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraPixelRect(target, new TweenSettings<UnityEngine.Rect>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraPixelRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraPixelRect(target, new TweenSettings<UnityEngine.Rect>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraPixelRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraPixelRect(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraPixelRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraPixelRect(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraPixelRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect endValue, TweenSettings settings) => CameraPixelRect(target, new TweenSettings<UnityEngine.Rect>(endValue, settings));
        public static Tween CameraPixelRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, TweenSettings settings) => CameraPixelRect(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, settings));
        public static Tween CameraPixelRect([NotNull] UnityEngine.Camera target, TweenSettings<UnityEngine.Rect> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.RectVal;
                _target.pixelRect = val;
            }, t => (t.target as UnityEngine.Camera).pixelRect.ToContainer(), TweenType.CameraPixelRect);
        }

        public static Tween CameraRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraRect(target, new TweenSettings<UnityEngine.Rect>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraRect(target, new TweenSettings<UnityEngine.Rect>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraRect(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => CameraRect(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween CameraRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect endValue, TweenSettings settings) => CameraRect(target, new TweenSettings<UnityEngine.Rect>(endValue, settings));
        public static Tween CameraRect([NotNull] UnityEngine.Camera target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, TweenSettings settings) => CameraRect(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, settings));
        public static Tween CameraRect([NotNull] UnityEngine.Camera target, TweenSettings<UnityEngine.Rect> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Camera;
                var val = _tween.RectVal;
                _target.rect = val;
            }, t => (t.target as UnityEngine.Camera).rect.ToContainer(), TweenType.CameraRect);
        }

        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, TweenSettings settings) => LocalRotation(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => LocalRotation(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));

        public static Tween Scale([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => Scale(target, new TweenSettings<float>(endValue, settings));
        public static Tween Scale([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => Scale(target, new TweenSettings<float>(startValue, endValue, settings));

        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, TweenSettings settings) => Rotation(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => Rotation(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));

        public static Tween Position([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, TweenSettings settings) => Position(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween Position([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => Position(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));
        public static Tween Position([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Vector3> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.Vector3Val;
                _target.position = val;
            }, t => (t.target as UnityEngine.Transform).position.ToContainer(), TweenType.Position);
        }

        public static Tween PositionX([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionX([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionX([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => PositionX(target, new TweenSettings<float>(endValue, settings));
        public static Tween PositionX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => PositionX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween PositionX([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.position = _target.position.WithComponent(0, val);
            }, t => (t.target as UnityEngine.Transform).position.x.ToContainer(), TweenType.PositionX);
        }

        public static Tween PositionY([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionY([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionY([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => PositionY(target, new TweenSettings<float>(endValue, settings));
        public static Tween PositionY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => PositionY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween PositionY([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.position = _target.position.WithComponent(1, val);
            }, t => (t.target as UnityEngine.Transform).position.y.ToContainer(), TweenType.PositionY);
        }

        public static Tween PositionZ([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionZ([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionZ([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => PositionZ(target, new TweenSettings<float>(endValue, settings));
        public static Tween PositionZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => PositionZ(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween PositionZ([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.position = _target.position.WithComponent(2, val);
            }, t => (t.target as UnityEngine.Transform).position.z.ToContainer(), TweenType.PositionZ);
        }

        public static Tween LocalPosition([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPosition(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPosition([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPosition(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPosition([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPosition(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPosition([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPosition(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPosition([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, TweenSettings settings) => LocalPosition(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween LocalPosition([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => LocalPosition(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));
        public static Tween LocalPosition([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Vector3> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.Vector3Val;
                _target.localPosition = val;
            }, t => (t.target as UnityEngine.Transform).localPosition.ToContainer(), TweenType.LocalPosition);
        }

        public static Tween LocalPositionX([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionX([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionX([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => LocalPositionX(target, new TweenSettings<float>(endValue, settings));
        public static Tween LocalPositionX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => LocalPositionX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween LocalPositionX([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.localPosition = _target.localPosition.WithComponent(0, val);
            }, t => (t.target as UnityEngine.Transform).localPosition.x.ToContainer(), TweenType.LocalPositionX);
        }

        public static Tween LocalPositionY([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionY([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionY([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => LocalPositionY(target, new TweenSettings<float>(endValue, settings));
        public static Tween LocalPositionY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => LocalPositionY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween LocalPositionY([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.localPosition = _target.localPosition.WithComponent(1, val);
            }, t => (t.target as UnityEngine.Transform).localPosition.y.ToContainer(), TweenType.LocalPositionY);
        }

        public static Tween LocalPositionZ([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionZ([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionZ([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => LocalPositionZ(target, new TweenSettings<float>(endValue, settings));
        public static Tween LocalPositionZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => LocalPositionZ(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween LocalPositionZ([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.localPosition = _target.localPosition.WithComponent(2, val);
            }, t => (t.target as UnityEngine.Transform).localPosition.z.ToContainer(), TweenType.LocalPositionZ);
        }

        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, TweenSettings settings) => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, settings));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, TweenSettings settings) => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, settings));
        public static Tween Rotation([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Quaternion> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.QuaternionVal;
                _target.rotation = val;
            }, t => (t.target as UnityEngine.Transform).rotation.ToContainer(), TweenType.RotationQuaternion);
        }

        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, TweenSettings settings) => LocalRotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, settings));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, TweenSettings settings) => LocalRotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, settings));
        public static Tween LocalRotation([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Quaternion> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.QuaternionVal;
                _target.localRotation = val;
            }, t => (t.target as UnityEngine.Transform).localRotation.ToContainer(), TweenType.LocalRotationQuaternion);
        }

        public static Tween Scale([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, TweenSettings settings) => Scale(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween Scale([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => Scale(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));
        public static Tween Scale([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Vector3> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.Vector3Val;
                _target.localScale = val;
            }, t => (t.target as UnityEngine.Transform).localScale.ToContainer(), TweenType.Scale);
        }

        public static Tween ScaleX([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleX([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleX([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => ScaleX(target, new TweenSettings<float>(endValue, settings));
        public static Tween ScaleX([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => ScaleX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween ScaleX([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.localScale = _target.localScale.WithComponent(0, val);
            }, t => (t.target as UnityEngine.Transform).localScale.x.ToContainer(), TweenType.ScaleX);
        }

        public static Tween ScaleY([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleY([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleY([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => ScaleY(target, new TweenSettings<float>(endValue, settings));
        public static Tween ScaleY([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => ScaleY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween ScaleY([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.localScale = _target.localScale.WithComponent(1, val);
            }, t => (t.target as UnityEngine.Transform).localScale.y.ToContainer(), TweenType.ScaleY);
        }

        public static Tween ScaleZ([NotNull] UnityEngine.Transform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleZ([NotNull] UnityEngine.Transform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween ScaleZ([NotNull] UnityEngine.Transform target, Single endValue, TweenSettings settings) => ScaleZ(target, new TweenSettings<float>(endValue, settings));
        public static Tween ScaleZ([NotNull] UnityEngine.Transform target, Single startValue, Single endValue, TweenSettings settings) => ScaleZ(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween ScaleZ([NotNull] UnityEngine.Transform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Transform;
                var val = _tween.FloatVal;
                _target.localScale = _target.localScale.WithComponent(2, val);
            }, t => (t.target as UnityEngine.Transform).localScale.z.ToContainer(), TweenType.ScaleZ);
        }

        public static Tween Color([NotNull] UnityEngine.SpriteRenderer target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.SpriteRenderer target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.SpriteRenderer target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.SpriteRenderer target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.SpriteRenderer target, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween Color([NotNull] UnityEngine.SpriteRenderer target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween Color([NotNull] UnityEngine.SpriteRenderer target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.SpriteRenderer;
                var val = _tween.ColorVal;
                _target.color = val;
            }, t => (t.target as UnityEngine.SpriteRenderer).color.ToContainer(), TweenType.Color);
        }

        public static Tween Alpha([NotNull] UnityEngine.SpriteRenderer target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.SpriteRenderer target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.SpriteRenderer target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.SpriteRenderer target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.SpriteRenderer target, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.SpriteRenderer target, Single startValue, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.SpriteRenderer target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.SpriteRenderer;
                var val = _tween.FloatVal;
                _target.color = _target.color.WithAlpha(val);
            }, t => (t.target as UnityEngine.SpriteRenderer).color.a.ToContainer(), TweenType.Alpha);
        }

        public static Tween TweenTimeScale([NotNull] PrimeTween.Tween target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Tween target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Tween target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Tween target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Tween target, Single endValue, TweenSettings settings) => TweenTimeScale(target, new TweenSettings<float>(endValue, settings));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Tween target, Single startValue, Single endValue, TweenSettings settings) => TweenTimeScale(target, new TweenSettings<float>(startValue, endValue, settings));

        public static Tween TweenTimeScale([NotNull] PrimeTween.Sequence target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Sequence target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Sequence target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Sequence target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TweenTimeScale(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Sequence target, Single endValue, TweenSettings settings) => TweenTimeScale(target, new TweenSettings<float>(endValue, settings));
        public static Tween TweenTimeScale([NotNull] PrimeTween.Sequence target, Single startValue, Single endValue, TweenSettings settings) => TweenTimeScale(target, new TweenSettings<float>(startValue, endValue, settings));

        #if !UNITY_2019_1_OR_NEWER || UNITY_UGUI_INSTALLED
        public static Tween UISliderValue([NotNull] UnityEngine.UI.Slider target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISliderValue(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISliderValue([NotNull] UnityEngine.UI.Slider target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISliderValue(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISliderValue([NotNull] UnityEngine.UI.Slider target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISliderValue(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISliderValue([NotNull] UnityEngine.UI.Slider target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISliderValue(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISliderValue([NotNull] UnityEngine.UI.Slider target, Single endValue, TweenSettings settings) => UISliderValue(target, new TweenSettings<float>(endValue, settings));
        public static Tween UISliderValue([NotNull] UnityEngine.UI.Slider target, Single startValue, Single endValue, TweenSettings settings) => UISliderValue(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UISliderValue([NotNull] UnityEngine.UI.Slider target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.Slider;
                var val = _tween.FloatVal;
                _target.value = val;
            }, t => (t.target as UnityEngine.UI.Slider).value.ToContainer(), TweenType.UISliderValue);
        }

        public static Tween UINormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UINormalizedPosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UINormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UINormalizedPosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UINormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UINormalizedPosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UINormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UINormalizedPosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UINormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, UnityEngine.Vector2 endValue, TweenSettings settings) => UINormalizedPosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UINormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UINormalizedPosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UINormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.ScrollRect;
                var val = _tween.Vector2Val;
                _target.SetNormalizedPosition(val);
            }, t => (t.target as UnityEngine.UI.ScrollRect).GetNormalizedPosition().ToContainer(), TweenType.UINormalizedPosition);
        }

        public static Tween UIHorizontalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIHorizontalNormalizedPosition(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIHorizontalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIHorizontalNormalizedPosition(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIHorizontalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIHorizontalNormalizedPosition(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIHorizontalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIHorizontalNormalizedPosition(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIHorizontalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single endValue, TweenSettings settings) => UIHorizontalNormalizedPosition(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIHorizontalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single startValue, Single endValue, TweenSettings settings) => UIHorizontalNormalizedPosition(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIHorizontalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.ScrollRect;
                var val = _tween.FloatVal;
                _target.horizontalNormalizedPosition = val;
            }, t => (t.target as UnityEngine.UI.ScrollRect).horizontalNormalizedPosition.ToContainer(), TweenType.UIHorizontalNormalizedPosition);
        }

        public static Tween UIVerticalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIVerticalNormalizedPosition(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIVerticalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIVerticalNormalizedPosition(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIVerticalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIVerticalNormalizedPosition(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIVerticalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIVerticalNormalizedPosition(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIVerticalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single endValue, TweenSettings settings) => UIVerticalNormalizedPosition(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIVerticalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, Single startValue, Single endValue, TweenSettings settings) => UIVerticalNormalizedPosition(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIVerticalNormalizedPosition([NotNull] UnityEngine.UI.ScrollRect target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.ScrollRect;
                var val = _tween.FloatVal;
                _target.verticalNormalizedPosition = val;
            }, t => (t.target as UnityEngine.UI.ScrollRect).verticalNormalizedPosition.ToContainer(), TweenType.UIVerticalNormalizedPosition);
        }

        public static Tween UIPivotX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotX([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIPivotX(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIPivotX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIPivotX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIPivotX([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.pivot = _target.pivot.WithComponent(0, val);
            }, t => (t.target as UnityEngine.RectTransform).pivot[0].ToContainer(), TweenType.UIPivotX);
        }

        public static Tween UIPivotY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivotY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivotY([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIPivotY(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIPivotY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIPivotY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIPivotY([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.pivot = _target.pivot.WithComponent(1, val);
            }, t => (t.target as UnityEngine.RectTransform).pivot[1].ToContainer(), TweenType.UIPivotY);
        }

        public static Tween UIPivot([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivot(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivot([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivot(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivot([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivot(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivot([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPivot(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPivot([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIPivot(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIPivot([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIPivot(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIPivot([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector2Val;
                _target.pivot = val;
            }, t => (t.target as UnityEngine.RectTransform).pivot.ToContainer(), TweenType.UIPivot);
        }

        public static Tween UIAnchorMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMax(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMax(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMax(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMax(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIAnchorMax(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIAnchorMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIAnchorMax(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIAnchorMax([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector2Val;
                _target.anchorMax = val;
            }, t => (t.target as UnityEngine.RectTransform).anchorMax.ToContainer(), TweenType.UIAnchorMax);
        }

        public static Tween UIAnchorMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMin(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMin(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMin(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchorMin(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchorMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIAnchorMin(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIAnchorMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIAnchorMin(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIAnchorMin([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector2Val;
                _target.anchorMin = val;
            }, t => (t.target as UnityEngine.RectTransform).anchorMin.ToContainer(), TweenType.UIAnchorMin);
        }

        public static Tween UIAnchoredPosition3D([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3D(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3D([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3D(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3D([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3D(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3D([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3D(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3D([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector3 endValue, TweenSettings settings) => UIAnchoredPosition3D(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween UIAnchoredPosition3D([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => UIAnchoredPosition3D(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));
        public static Tween UIAnchoredPosition3D([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector3> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector3Val;
                _target.anchoredPosition3D = val;
            }, t => (t.target as UnityEngine.RectTransform).anchoredPosition3D.ToContainer(), TweenType.UIAnchoredPosition3D);
        }

        public static Tween UIAnchoredPosition3DX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DX([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIAnchoredPosition3DX(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIAnchoredPosition3DX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIAnchoredPosition3DX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIAnchoredPosition3DX([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.anchoredPosition3D = _target.anchoredPosition3D.WithComponent(0, val);
            }, t => (t.target as UnityEngine.RectTransform).anchoredPosition3D[0].ToContainer(), TweenType.UIAnchoredPosition3DX);
        }

        public static Tween UIAnchoredPosition3DY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DY([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIAnchoredPosition3DY(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIAnchoredPosition3DY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIAnchoredPosition3DY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIAnchoredPosition3DY([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.anchoredPosition3D = _target.anchoredPosition3D.WithComponent(1, val);
            }, t => (t.target as UnityEngine.RectTransform).anchoredPosition3D[1].ToContainer(), TweenType.UIAnchoredPosition3DY);
        }

        public static Tween UIAnchoredPosition3DZ([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DZ([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DZ(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DZ([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DZ([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition3DZ(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition3DZ([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIAnchoredPosition3DZ(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIAnchoredPosition3DZ([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIAnchoredPosition3DZ(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIAnchoredPosition3DZ([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.anchoredPosition3D = _target.anchoredPosition3D.WithComponent(2, val);
            }, t => (t.target as UnityEngine.RectTransform).anchoredPosition3D[2].ToContainer(), TweenType.UIAnchoredPosition3DZ);
        }

        public static Tween UIEffectDistance([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIEffectDistance(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIEffectDistance([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIEffectDistance(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIEffectDistance([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIEffectDistance(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIEffectDistance([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIEffectDistance(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIEffectDistance([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIEffectDistance(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIEffectDistance([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIEffectDistance(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIEffectDistance([NotNull] UnityEngine.UI.Shadow target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.Shadow;
                var val = _tween.Vector2Val;
                _target.effectDistance = val;
            }, t => (t.target as UnityEngine.UI.Shadow).effectDistance.ToContainer(), TweenType.UIEffectDistance);
        }

        public static Tween Alpha([NotNull] UnityEngine.UI.Shadow target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Shadow target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Shadow target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Shadow target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Shadow target, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.UI.Shadow target, Single startValue, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.UI.Shadow target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.Shadow;
                var val = _tween.FloatVal;
                _target.effectColor = _target.effectColor.WithAlpha(val);
            }, t => (t.target as UnityEngine.UI.Shadow).effectColor.a.ToContainer(), TweenType.UIAlphaShadow);
        }

        public static Tween Color([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween Color([NotNull] UnityEngine.UI.Shadow target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween Color([NotNull] UnityEngine.UI.Shadow target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.Shadow;
                var val = _tween.ColorVal;
                _target.effectColor = val;
            }, t => (t.target as UnityEngine.UI.Shadow).effectColor.ToContainer(), TweenType.UIColorShadow);
        }

        public static Tween UIPreferredSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIPreferredSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIPreferredSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIPreferredSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIPreferredSize([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.Vector2Val;
                _target.SetPreferredSize(val);
            }, t => (t.target as UnityEngine.UI.LayoutElement).GetPreferredSize().ToContainer(), TweenType.UIPreferredSize);
        }

        public static Tween UIPreferredWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredWidth(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredWidth(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredWidth(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredWidth(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, TweenSettings settings) => UIPreferredWidth(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIPreferredWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, TweenSettings settings) => UIPreferredWidth(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIPreferredWidth([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.FloatVal;
                _target.preferredWidth = val;
            }, t => (t.target as UnityEngine.UI.LayoutElement).preferredWidth.ToContainer(), TweenType.UIPreferredWidth);
        }

        public static Tween UIPreferredHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredHeight(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredHeight(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredHeight(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIPreferredHeight(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIPreferredHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, TweenSettings settings) => UIPreferredHeight(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIPreferredHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, TweenSettings settings) => UIPreferredHeight(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIPreferredHeight([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.FloatVal;
                _target.preferredHeight = val;
            }, t => (t.target as UnityEngine.UI.LayoutElement).preferredHeight.ToContainer(), TweenType.UIPreferredHeight);
        }

        public static Tween UIFlexibleSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIFlexibleSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIFlexibleSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIFlexibleSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIFlexibleSize([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.Vector2Val;
                _target.SetFlexibleSize(val);
            }, t => (t.target as UnityEngine.UI.LayoutElement).GetFlexibleSize().ToContainer(), TweenType.UIFlexibleSize);
        }

        public static Tween UIFlexibleWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleWidth(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleWidth(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleWidth(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleWidth(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, TweenSettings settings) => UIFlexibleWidth(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIFlexibleWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, TweenSettings settings) => UIFlexibleWidth(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIFlexibleWidth([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.FloatVal;
                _target.flexibleWidth = val;
            }, t => (t.target as UnityEngine.UI.LayoutElement).flexibleWidth.ToContainer(), TweenType.UIFlexibleWidth);
        }

        public static Tween UIFlexibleHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleHeight(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleHeight(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleHeight(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFlexibleHeight(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFlexibleHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, TweenSettings settings) => UIFlexibleHeight(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIFlexibleHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, TweenSettings settings) => UIFlexibleHeight(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIFlexibleHeight([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.FloatVal;
                _target.flexibleHeight = val;
            }, t => (t.target as UnityEngine.UI.LayoutElement).flexibleHeight.ToContainer(), TweenType.UIFlexibleHeight);
        }

        public static Tween UIMinSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIMinSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIMinSize([NotNull] UnityEngine.UI.LayoutElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIMinSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIMinSize([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.Vector2Val;
                _target.SetMinSize(val);
            }, t => (t.target as UnityEngine.UI.LayoutElement).GetMinSize().ToContainer(), TweenType.UIMinSize);
        }

        public static Tween UIMinWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinWidth(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinWidth(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinWidth(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinWidth(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinWidth([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, TweenSettings settings) => UIMinWidth(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIMinWidth([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, TweenSettings settings) => UIMinWidth(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIMinWidth([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.FloatVal;
                _target.minWidth = val;
            }, t => (t.target as UnityEngine.UI.LayoutElement).minWidth.ToContainer(), TweenType.UIMinWidth);
        }

        public static Tween UIMinHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinHeight(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinHeight(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinHeight(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIMinHeight(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIMinHeight([NotNull] UnityEngine.UI.LayoutElement target, Single endValue, TweenSettings settings) => UIMinHeight(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIMinHeight([NotNull] UnityEngine.UI.LayoutElement target, Single startValue, Single endValue, TweenSettings settings) => UIMinHeight(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIMinHeight([NotNull] UnityEngine.UI.LayoutElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.LayoutElement;
                var val = _tween.FloatVal;
                _target.minHeight = val;
            }, t => (t.target as UnityEngine.UI.LayoutElement).minHeight.ToContainer(), TweenType.UIMinHeight);
        }

        public static Tween Color([NotNull] UnityEngine.UI.Graphic target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Graphic target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Graphic target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Graphic target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UI.Graphic target, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween Color([NotNull] UnityEngine.UI.Graphic target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween Color([NotNull] UnityEngine.UI.Graphic target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.Graphic;
                var val = _tween.ColorVal;
                _target.color = val;
            }, t => (t.target as UnityEngine.UI.Graphic).color.ToContainer(), TweenType.UIColorGraphic);
        }

        public static Tween UIAnchoredPosition([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPosition([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIAnchoredPosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIAnchoredPosition([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIAnchoredPosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIAnchoredPosition([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector2Val;
                _target.anchoredPosition = val;
            }, t => (t.target as UnityEngine.RectTransform).anchoredPosition.ToContainer(), TweenType.UIAnchoredPosition);
        }

        public static Tween UIAnchoredPositionX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionX([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIAnchoredPositionX(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIAnchoredPositionX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIAnchoredPositionX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIAnchoredPositionX([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.anchoredPosition = _target.anchoredPosition.WithComponent(0, val);
            }, t => (t.target as UnityEngine.RectTransform).anchoredPosition.x.ToContainer(), TweenType.UIAnchoredPositionX);
        }

        public static Tween UIAnchoredPositionY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIAnchoredPositionY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIAnchoredPositionY([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIAnchoredPositionY(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIAnchoredPositionY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIAnchoredPositionY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIAnchoredPositionY([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.anchoredPosition = _target.anchoredPosition.WithComponent(1, val);
            }, t => (t.target as UnityEngine.RectTransform).anchoredPosition.y.ToContainer(), TweenType.UIAnchoredPositionY);
        }

        public static Tween UISizeDelta([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISizeDelta(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISizeDelta([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISizeDelta(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISizeDelta([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISizeDelta(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISizeDelta([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UISizeDelta(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UISizeDelta([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, TweenSettings settings) => UISizeDelta(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UISizeDelta([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UISizeDelta(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UISizeDelta([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector2Val;
                _target.sizeDelta = val;
            }, t => (t.target as UnityEngine.RectTransform).sizeDelta.ToContainer(), TweenType.UISizeDelta);
        }

        public static Tween Alpha([NotNull] UnityEngine.CanvasGroup target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.CanvasGroup target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.CanvasGroup target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.CanvasGroup target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.CanvasGroup target, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.CanvasGroup target, Single startValue, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.CanvasGroup target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.CanvasGroup;
                var val = _tween.FloatVal;
                _target.alpha = val;
            }, t => (t.target as UnityEngine.CanvasGroup).alpha.ToContainer(), TweenType.UIAlphaCanvasGroup);
        }

        public static Tween Alpha([NotNull] UnityEngine.UI.Graphic target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Graphic target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Graphic target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Graphic target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UI.Graphic target, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.UI.Graphic target, Single startValue, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.UI.Graphic target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.Graphic;
                var val = _tween.FloatVal;
                _target.color = _target.color.WithAlpha(val);
            }, t => (t.target as UnityEngine.UI.Graphic).color.a.ToContainer(), TweenType.UIAlphaGraphic);
        }

        public static Tween UIFillAmount([NotNull] UnityEngine.UI.Image target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFillAmount(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFillAmount([NotNull] UnityEngine.UI.Image target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFillAmount(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFillAmount([NotNull] UnityEngine.UI.Image target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFillAmount(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFillAmount([NotNull] UnityEngine.UI.Image target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIFillAmount(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIFillAmount([NotNull] UnityEngine.UI.Image target, Single endValue, TweenSettings settings) => UIFillAmount(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIFillAmount([NotNull] UnityEngine.UI.Image target, Single startValue, Single endValue, TweenSettings settings) => UIFillAmount(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIFillAmount([NotNull] UnityEngine.UI.Image target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UI.Image;
                var val = _tween.FloatVal;
                _target.fillAmount = val;
            }, t => (t.target as UnityEngine.UI.Image).fillAmount.ToContainer(), TweenType.UIFillAmount);
        }

        public static Tween UIOffsetMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMin(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMin(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMin(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMin(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIOffsetMin(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIOffsetMin([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIOffsetMin(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIOffsetMin([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector2Val;
                _target.offsetMin = val;
            }, t => (t.target as UnityEngine.RectTransform).offsetMin.ToContainer(), TweenType.UIOffsetMin);
        }

        public static Tween UIOffsetMinX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinX([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIOffsetMinX(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIOffsetMinX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIOffsetMinX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIOffsetMinX([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.offsetMin = _target.offsetMin.WithComponent(0, val);
            }, t => (t.target as UnityEngine.RectTransform).offsetMin[0].ToContainer(), TweenType.UIOffsetMinX);
        }

        public static Tween UIOffsetMinY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMinY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMinY([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIOffsetMinY(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIOffsetMinY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIOffsetMinY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIOffsetMinY([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.offsetMin = _target.offsetMin.WithComponent(1, val);
            }, t => (t.target as UnityEngine.RectTransform).offsetMin[1].ToContainer(), TweenType.UIOffsetMinY);
        }

        public static Tween UIOffsetMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMax(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMax(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMax(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMax(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 endValue, TweenSettings settings) => UIOffsetMax(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween UIOffsetMax([NotNull] UnityEngine.RectTransform target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => UIOffsetMax(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween UIOffsetMax([NotNull] UnityEngine.RectTransform target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.Vector2Val;
                _target.offsetMax = val;
            }, t => (t.target as UnityEngine.RectTransform).offsetMax.ToContainer(), TweenType.UIOffsetMax);
        }

        public static Tween UIOffsetMaxX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxX([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxX(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxX(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxX([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIOffsetMaxX(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIOffsetMaxX([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIOffsetMaxX(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIOffsetMaxX([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.offsetMax = _target.offsetMax.WithComponent(0, val);
            }, t => (t.target as UnityEngine.RectTransform).offsetMax[0].ToContainer(), TweenType.UIOffsetMaxX);
        }

        public static Tween UIOffsetMaxY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxY([NotNull] UnityEngine.RectTransform target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxY(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => UIOffsetMaxY(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween UIOffsetMaxY([NotNull] UnityEngine.RectTransform target, Single endValue, TweenSettings settings) => UIOffsetMaxY(target, new TweenSettings<float>(endValue, settings));
        public static Tween UIOffsetMaxY([NotNull] UnityEngine.RectTransform target, Single startValue, Single endValue, TweenSettings settings) => UIOffsetMaxY(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween UIOffsetMaxY([NotNull] UnityEngine.RectTransform target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.RectTransform;
                var val = _tween.FloatVal;
                _target.offsetMax = _target.offsetMax.WithComponent(1, val);
            }, t => (t.target as UnityEngine.RectTransform).offsetMax[1].ToContainer(), TweenType.UIOffsetMaxY);
        }

        #endif
        #if !UNITY_2019_1_OR_NEWER || PHYSICS_MODULE_INSTALLED
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody target, UnityEngine.Vector3 endValue, TweenSettings settings) => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody target, TweenSettings<UnityEngine.Vector3> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Rigidbody;
                var val = _tween.Vector3Val;
                _target.MovePosition(val);
            }, t => (t.target as UnityEngine.Rigidbody).position.ToContainer(), TweenType.RigidbodyMovePosition);
        }

        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody target, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody target, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody target, UnityEngine.Quaternion endValue, TweenSettings settings) => RigidbodyMoveRotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, settings));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, TweenSettings settings) => RigidbodyMoveRotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, settings));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody target, TweenSettings<UnityEngine.Quaternion> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Rigidbody;
                var val = _tween.QuaternionVal;
                _target.MoveRotation(val);
            }, t => (t.target as UnityEngine.Rigidbody).rotation.ToContainer(), TweenType.RigidbodyMoveRotation);
        }

        #endif
        #if !UNITY_2019_1_OR_NEWER || PHYSICS2D_MODULE_INSTALLED
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody2D target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody2D target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody2D target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody2D target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody2D target, UnityEngine.Vector2 endValue, TweenSettings settings) => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody2D target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => RigidbodyMovePosition(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween RigidbodyMovePosition([NotNull] UnityEngine.Rigidbody2D target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Rigidbody2D;
                var val = _tween.Vector2Val;
                _target.MovePosition(val);
            }, t => (t.target as UnityEngine.Rigidbody2D).position.ToContainer(), TweenType.RigidbodyMovePosition2D);
        }

        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody2D target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody2D target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody2D target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody2D target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RigidbodyMoveRotation(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody2D target, Single endValue, TweenSettings settings) => RigidbodyMoveRotation(target, new TweenSettings<float>(endValue, settings));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody2D target, Single startValue, Single endValue, TweenSettings settings) => RigidbodyMoveRotation(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween RigidbodyMoveRotation([NotNull] UnityEngine.Rigidbody2D target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Rigidbody2D;
                var val = _tween.FloatVal;
                _target.MoveRotation(val);
            }, t => (t.target as UnityEngine.Rigidbody2D).rotation.ToContainer(), TweenType.RigidbodyMoveRotation2D);
        }

        #endif
        public static Tween MaterialColor([NotNull] UnityEngine.Material target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialColor([NotNull] UnityEngine.Material target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialColor([NotNull] UnityEngine.Material target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialColor([NotNull] UnityEngine.Material target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialColor([NotNull] UnityEngine.Material target, UnityEngine.Color endValue, TweenSettings settings) => MaterialColor(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween MaterialColor([NotNull] UnityEngine.Material target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => MaterialColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween MaterialColor([NotNull] UnityEngine.Material target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Material;
                var val = _tween.ColorVal;
                _target.color = val;
            }, t => (t.target as UnityEngine.Material).color.ToContainer(), TweenType.MaterialColor);
        }

        public static Tween MaterialAlpha([NotNull] UnityEngine.Material target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialAlpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialAlpha([NotNull] UnityEngine.Material target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialAlpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialAlpha([NotNull] UnityEngine.Material target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialAlpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialAlpha([NotNull] UnityEngine.Material target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialAlpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialAlpha([NotNull] UnityEngine.Material target, Single endValue, TweenSettings settings) => MaterialAlpha(target, new TweenSettings<float>(endValue, settings));
        public static Tween MaterialAlpha([NotNull] UnityEngine.Material target, Single startValue, Single endValue, TweenSettings settings) => MaterialAlpha(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween MaterialAlpha([NotNull] UnityEngine.Material target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Material;
                var val = _tween.FloatVal;
                _target.color = _target.color.WithAlpha(val);
            }, t => (t.target as UnityEngine.Material).color.a.ToContainer(), TweenType.MaterialAlpha);
        }

        public static Tween MaterialMainTextureOffset([NotNull] UnityEngine.Material target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureOffset(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureOffset([NotNull] UnityEngine.Material target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureOffset(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureOffset([NotNull] UnityEngine.Material target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureOffset(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureOffset([NotNull] UnityEngine.Material target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureOffset(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureOffset([NotNull] UnityEngine.Material target, UnityEngine.Vector2 endValue, TweenSettings settings) => MaterialMainTextureOffset(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween MaterialMainTextureOffset([NotNull] UnityEngine.Material target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => MaterialMainTextureOffset(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween MaterialMainTextureOffset([NotNull] UnityEngine.Material target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Material;
                var val = _tween.Vector2Val;
                _target.mainTextureOffset = val;
            }, t => (t.target as UnityEngine.Material).mainTextureOffset.ToContainer(), TweenType.MaterialMainTextureOffset);
        }

        public static Tween MaterialMainTextureScale([NotNull] UnityEngine.Material target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureScale(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureScale([NotNull] UnityEngine.Material target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureScale(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureScale([NotNull] UnityEngine.Material target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureScale(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureScale([NotNull] UnityEngine.Material target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => MaterialMainTextureScale(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween MaterialMainTextureScale([NotNull] UnityEngine.Material target, UnityEngine.Vector2 endValue, TweenSettings settings) => MaterialMainTextureScale(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween MaterialMainTextureScale([NotNull] UnityEngine.Material target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => MaterialMainTextureScale(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween MaterialMainTextureScale([NotNull] UnityEngine.Material target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.Material;
                var val = _tween.Vector2Val;
                _target.mainTextureScale = val;
            }, t => (t.target as UnityEngine.Material).mainTextureScale.ToContainer(), TweenType.MaterialMainTextureScale);
        }

        #if !UNITY_2019_1_OR_NEWER || AUDIO_MODULE_INSTALLED
        public static Tween AudioVolume([NotNull] UnityEngine.AudioSource target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioVolume(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioVolume([NotNull] UnityEngine.AudioSource target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioVolume(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioVolume([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioVolume(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioVolume([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioVolume(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioVolume([NotNull] UnityEngine.AudioSource target, Single endValue, TweenSettings settings) => AudioVolume(target, new TweenSettings<float>(endValue, settings));
        public static Tween AudioVolume([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, TweenSettings settings) => AudioVolume(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween AudioVolume([NotNull] UnityEngine.AudioSource target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.AudioSource;
                var val = _tween.FloatVal;
                _target.volume = val;
            }, t => (t.target as UnityEngine.AudioSource).volume.ToContainer(), TweenType.AudioVolume);
        }

        public static Tween AudioPitch([NotNull] UnityEngine.AudioSource target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPitch(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPitch([NotNull] UnityEngine.AudioSource target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPitch(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPitch([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPitch(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPitch([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPitch(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPitch([NotNull] UnityEngine.AudioSource target, Single endValue, TweenSettings settings) => AudioPitch(target, new TweenSettings<float>(endValue, settings));
        public static Tween AudioPitch([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, TweenSettings settings) => AudioPitch(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween AudioPitch([NotNull] UnityEngine.AudioSource target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.AudioSource;
                var val = _tween.FloatVal;
                _target.pitch = val;
            }, t => (t.target as UnityEngine.AudioSource).pitch.ToContainer(), TweenType.AudioPitch);
        }

        public static Tween AudioPanStereo([NotNull] UnityEngine.AudioSource target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPanStereo(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPanStereo([NotNull] UnityEngine.AudioSource target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPanStereo(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPanStereo([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPanStereo(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPanStereo([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => AudioPanStereo(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween AudioPanStereo([NotNull] UnityEngine.AudioSource target, Single endValue, TweenSettings settings) => AudioPanStereo(target, new TweenSettings<float>(endValue, settings));
        public static Tween AudioPanStereo([NotNull] UnityEngine.AudioSource target, Single startValue, Single endValue, TweenSettings settings) => AudioPanStereo(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween AudioPanStereo([NotNull] UnityEngine.AudioSource target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.AudioSource;
                var val = _tween.FloatVal;
                _target.panStereo = val;
            }, t => (t.target as UnityEngine.AudioSource).panStereo.ToContainer(), TweenType.AudioPanStereo);
        }

        #endif
        #if UI_ELEMENTS_MODULE_INSTALLED
        public static Tween VisualElementLayout([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Rect endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementLayout(target, new TweenSettings<UnityEngine.Rect>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementLayout([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Rect endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementLayout(target, new TweenSettings<UnityEngine.Rect>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementLayout([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementLayout(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementLayout([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementLayout(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementLayout([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Rect endValue, TweenSettings settings) => VisualElementLayout(target, new TweenSettings<UnityEngine.Rect>(endValue, settings));
        public static Tween VisualElementLayout([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, TweenSettings settings) => VisualElementLayout(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, settings));
        public static Tween VisualElementLayout([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<UnityEngine.Rect> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.RectVal;
                _target.SetStyleRect(val);
            }, t => (t.target as UnityEngine.UIElements.VisualElement).GetResolvedStyleRect().ToContainer(), TweenType.VisualElementLayout);
        }

        public static Tween Position([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Position(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Position([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 endValue, TweenSettings settings) => Position(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween Position([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => Position(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));
        public static Tween Position([NotNull] UnityEngine.UIElements.ITransform target, TweenSettings<UnityEngine.Vector3> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.ITransform;
                var val = _tween.Vector3Val;
                _target.position = val;
            }, t => (t.target as UnityEngine.UIElements.ITransform).position.ToContainer(), TweenType.VisualElementPosition);
        }

        public static Tween Rotation([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Rotation([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Quaternion endValue, TweenSettings settings) => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(endValue, settings));
        public static Tween Rotation([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, TweenSettings settings) => Rotation(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, settings));
        public static Tween Rotation([NotNull] UnityEngine.UIElements.ITransform target, TweenSettings<UnityEngine.Quaternion> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.ITransform;
                var val = _tween.QuaternionVal;
                _target.rotation = val;
            }, t => (t.target as UnityEngine.UIElements.ITransform).rotation.ToContainer(), TweenType.VisualElementRotationQuaternion);
        }

        public static Tween Scale([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Scale(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Scale([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 endValue, TweenSettings settings) => Scale(target, new TweenSettings<UnityEngine.Vector3>(endValue, settings));
        public static Tween Scale([NotNull] UnityEngine.UIElements.ITransform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings) => Scale(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings));
        public static Tween Scale([NotNull] UnityEngine.UIElements.ITransform target, TweenSettings<UnityEngine.Vector3> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.ITransform;
                var val = _tween.Vector3Val;
                _target.scale = val;
            }, t => (t.target as UnityEngine.UIElements.ITransform).scale.ToContainer(), TweenType.VisualElementScale);
        }

        public static Tween VisualElementSize([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementSize([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementSize([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementSize([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementSize([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 endValue, TweenSettings settings) => VisualElementSize(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween VisualElementSize([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => VisualElementSize(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween VisualElementSize([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.Vector2Val;
                _target.style.width = val.x; _target.style.height = val.y;
            }, t => (t.target as UnityEngine.UIElements.VisualElement).layout.size.ToContainer(), TweenType.VisualElementSize);
        }

        public static Tween VisualElementTopLeft([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementTopLeft(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementTopLeft([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementTopLeft(target, new TweenSettings<UnityEngine.Vector2>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementTopLeft([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementTopLeft(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementTopLeft([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementTopLeft(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementTopLeft([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 endValue, TweenSettings settings) => VisualElementTopLeft(target, new TweenSettings<UnityEngine.Vector2>(endValue, settings));
        public static Tween VisualElementTopLeft([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings) => VisualElementTopLeft(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings));
        public static Tween VisualElementTopLeft([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<UnityEngine.Vector2> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.Vector2Val;
                _target.SetTopLeft(val);
            }, t => (t.target as UnityEngine.UIElements.VisualElement).GetTopLeft().ToContainer(), TweenType.VisualElementTopLeft);
        }

        public static Tween VisualElementColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, TweenSettings settings) => VisualElementColor(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween VisualElementColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => VisualElementColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween VisualElementColor([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.ColorVal;
                _target.style.color = val;
            }, t => (t.target as UnityEngine.UIElements.VisualElement).style.color.value.ToContainer(), TweenType.VisualElementColor);
        }

        public static Tween Color([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Color([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween Color([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => Color(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween Color([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.ColorVal;
                _target.style.color = val;
            }, t => (t.target as UnityEngine.UIElements.VisualElement).style.color.value.ToContainer(), TweenType.VisualElementColor);
        }

        public static Tween VisualElementBackgroundColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementBackgroundColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementBackgroundColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementBackgroundColor(target, new TweenSettings<UnityEngine.Color>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementBackgroundColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementBackgroundColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementBackgroundColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementBackgroundColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementBackgroundColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color endValue, TweenSettings settings) => VisualElementBackgroundColor(target, new TweenSettings<UnityEngine.Color>(endValue, settings));
        public static Tween VisualElementBackgroundColor([NotNull] UnityEngine.UIElements.VisualElement target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings) => VisualElementBackgroundColor(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings));
        public static Tween VisualElementBackgroundColor([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<UnityEngine.Color> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.ColorVal;
                _target.style.backgroundColor = val;
            }, t => (t.target as UnityEngine.UIElements.VisualElement).style.backgroundColor.value.ToContainer(), TweenType.VisualElementBackgroundColor);
        }

        public static Tween VisualElementOpacity([NotNull] UnityEngine.UIElements.VisualElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementOpacity(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementOpacity([NotNull] UnityEngine.UIElements.VisualElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementOpacity(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementOpacity([NotNull] UnityEngine.UIElements.VisualElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementOpacity(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementOpacity([NotNull] UnityEngine.UIElements.VisualElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => VisualElementOpacity(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween VisualElementOpacity([NotNull] UnityEngine.UIElements.VisualElement target, Single endValue, TweenSettings settings) => VisualElementOpacity(target, new TweenSettings<float>(endValue, settings));
        public static Tween VisualElementOpacity([NotNull] UnityEngine.UIElements.VisualElement target, Single startValue, Single endValue, TweenSettings settings) => VisualElementOpacity(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween VisualElementOpacity([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.FloatVal;
                _target.style.opacity = val;
            }, t => (t.target as UnityEngine.UIElements.VisualElement).style.opacity.value.ToContainer(), TweenType.VisualElementOpacity);
        }

        public static Tween Alpha([NotNull] UnityEngine.UIElements.VisualElement target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UIElements.VisualElement target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UIElements.VisualElement target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UIElements.VisualElement target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Alpha(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween Alpha([NotNull] UnityEngine.UIElements.VisualElement target, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.UIElements.VisualElement target, Single startValue, Single endValue, TweenSettings settings) => Alpha(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween Alpha([NotNull] UnityEngine.UIElements.VisualElement target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as UnityEngine.UIElements.VisualElement;
                var val = _tween.FloatVal;
                _target.style.opacity = val;
            }, t => (t.target as UnityEngine.UIElements.VisualElement).style.opacity.value.ToContainer(), TweenType.VisualElementOpacity);
        }

        #endif
        #if TEXT_MESH_PRO_INSTALLED
        public static Tween TextMaxVisibleCharacters([NotNull] TMPro.TMP_Text target, int endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextMaxVisibleCharacters(target, new TweenSettings<int>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextMaxVisibleCharacters([NotNull] TMPro.TMP_Text target, int endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextMaxVisibleCharacters(target, new TweenSettings<int>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextMaxVisibleCharacters([NotNull] TMPro.TMP_Text target, int startValue, int endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextMaxVisibleCharacters(target, new TweenSettings<int>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextMaxVisibleCharacters([NotNull] TMPro.TMP_Text target, int startValue, int endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextMaxVisibleCharacters(target, new TweenSettings<int>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextMaxVisibleCharacters([NotNull] TMPro.TMP_Text target, int endValue, TweenSettings settings) => TextMaxVisibleCharacters(target, new TweenSettings<int>(endValue, settings));
        public static Tween TextMaxVisibleCharacters([NotNull] TMPro.TMP_Text target, int startValue, int endValue, TweenSettings settings) => TextMaxVisibleCharacters(target, new TweenSettings<int>(startValue, endValue, settings));

        public static Tween TextFontSize([NotNull] TMPro.TMP_Text target, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextFontSize(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextFontSize([NotNull] TMPro.TMP_Text target, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextFontSize(target, new TweenSettings<float>(endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextFontSize([NotNull] TMPro.TMP_Text target, Single startValue, Single endValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextFontSize(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextFontSize([NotNull] TMPro.TMP_Text target, Single startValue, Single endValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => TextFontSize(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween TextFontSize([NotNull] TMPro.TMP_Text target, Single endValue, TweenSettings settings) => TextFontSize(target, new TweenSettings<float>(endValue, settings));
        public static Tween TextFontSize([NotNull] TMPro.TMP_Text target, Single startValue, Single endValue, TweenSettings settings) => TextFontSize(target, new TweenSettings<float>(startValue, endValue, settings));
        public static Tween TextFontSize([NotNull] TMPro.TMP_Text target, TweenSettings<float> settings) {
            return animate(target, ref settings, _tween => {
                var _target = _tween.target as TMPro.TMP_Text;
                var val = _tween.FloatVal;
                _target.fontSize = val;
            }, t => (t.target as TMPro.TMP_Text).fontSize.ToContainer(), TweenType.TextFontSize);
        }

        #endif

        public static Tween Custom(float startValue, float endValue, float duration, [NotNull] Action<float> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(float startValue, float endValue, float duration, [NotNull] Action<float> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(float startValue, float endValue, TweenSettings settings, [NotNull] Action<float> onValueChange) => Custom(new TweenSettings<float>(startValue, endValue, settings), onValueChange);
        public static Tween Custom(TweenSettings<float> settings, [NotNull] Action<float> onValueChange) {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.Setup(PrimeTweenManager.dummyTarget, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<float>;
                var val = _tween.FloatVal;
                try {
                    _onValueChange(val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomFloat);
            return PrimeTweenManager.Animate(tween);
        }
        public static Tween Custom<T>([NotNull] T target, float startValue, float endValue, float duration, [NotNull] Action<T, float> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, float startValue, float endValue, float duration, [NotNull] Action<T, float> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<float>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, float startValue, float endValue, TweenSettings settings, [NotNull] Action<T, float> onValueChange) where T : class 
            => Custom_internal(target, new TweenSettings<float>(startValue, endValue, settings), onValueChange);
        public static Tween Custom<T>([NotNull] T target, TweenSettings<float> settings, [NotNull] Action<T, float> onValueChange) where T : class 
            => Custom_internal(target, settings, onValueChange);
        #if PRIME_TWEEN_EXPERIMENTAL
        public static Tween CustomAdditive<T>([NotNull] T target, float deltaValue, TweenSettings settings, [NotNull] Action<T, float> onDeltaChange) where T : class 
            => Custom_internal(target, new TweenSettings<float>(default, deltaValue, settings), onDeltaChange, true);
        #endif
        static Tween Custom_internal<T>([NotNull] T target, TweenSettings<float> settings, [NotNull] Action<T, float> onValueChange, bool isAdditive = false) where T : class {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.isAdditive = isAdditive;
            tween.Setup(target, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<T, float>;
                var _target = _tween.target as T;
                float val;
                if (_tween.isAdditive) {
                    var newVal = _tween.FloatVal;
                    val = newVal.calcDelta(_tween.prevVal);
                    _tween.prevVal.FloatVal = newVal;
                } else {
                    val = _tween.FloatVal;
                }
                try {
                    _onValueChange(_target, val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e, _target as UnityEngine.Object);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomFloat);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animate(object target, ref TweenSettings<float> settings, [NotNull] Action<ReusableTween> setter, Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animateWithIntParam([NotNull] object target, int intParam, ref TweenSettings<float> settings, [NotNull] Action<ReusableTween> setter, [NotNull] Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.intParam = intParam;
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }

        public static Tween Custom(UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, [NotNull] Action<UnityEngine.Color> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, [NotNull] Action<UnityEngine.Color> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings, [NotNull] Action<UnityEngine.Color> onValueChange) => Custom(new TweenSettings<UnityEngine.Color>(startValue, endValue, settings), onValueChange);
        public static Tween Custom(TweenSettings<UnityEngine.Color> settings, [NotNull] Action<UnityEngine.Color> onValueChange) {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.Setup(PrimeTweenManager.dummyTarget, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<UnityEngine.Color>;
                var val = _tween.ColorVal;
                try {
                    _onValueChange(val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomColor);
            return PrimeTweenManager.Animate(tween);
        }
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, [NotNull] Action<T, UnityEngine.Color> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Color startValue, UnityEngine.Color endValue, float duration, [NotNull] Action<T, UnityEngine.Color> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Color startValue, UnityEngine.Color endValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Color> onValueChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Color>(startValue, endValue, settings), onValueChange);
        public static Tween Custom<T>([NotNull] T target, TweenSettings<UnityEngine.Color> settings, [NotNull] Action<T, UnityEngine.Color> onValueChange) where T : class 
            => Custom_internal(target, settings, onValueChange);
        #if PRIME_TWEEN_EXPERIMENTAL
        public static Tween CustomAdditive<T>([NotNull] T target, UnityEngine.Color deltaValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Color> onDeltaChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Color>(default, deltaValue, settings), onDeltaChange, true);
        #endif
        static Tween Custom_internal<T>([NotNull] T target, TweenSettings<UnityEngine.Color> settings, [NotNull] Action<T, UnityEngine.Color> onValueChange, bool isAdditive = false) where T : class {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.isAdditive = isAdditive;
            tween.Setup(target, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<T, UnityEngine.Color>;
                var _target = _tween.target as T;
                UnityEngine.Color val;
                if (_tween.isAdditive) {
                    var newVal = _tween.ColorVal;
                    val = newVal.calcDelta(_tween.prevVal);
                    _tween.prevVal.ColorVal = newVal;
                } else {
                    val = _tween.ColorVal;
                }
                try {
                    _onValueChange(_target, val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e, _target as UnityEngine.Object);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomColor);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animate(object target, ref TweenSettings<UnityEngine.Color> settings, [NotNull] Action<ReusableTween> setter, Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animateWithIntParam([NotNull] object target, int intParam, ref TweenSettings<UnityEngine.Color> settings, [NotNull] Action<ReusableTween> setter, [NotNull] Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.intParam = intParam;
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }

        public static Tween Custom(UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, [NotNull] Action<UnityEngine.Vector2> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, [NotNull] Action<UnityEngine.Vector2> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings, [NotNull] Action<UnityEngine.Vector2> onValueChange) => Custom(new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings), onValueChange);
        public static Tween Custom(TweenSettings<UnityEngine.Vector2> settings, [NotNull] Action<UnityEngine.Vector2> onValueChange) {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.Setup(PrimeTweenManager.dummyTarget, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<UnityEngine.Vector2>;
                var val = _tween.Vector2Val;
                try {
                    _onValueChange(val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomVector2);
            return PrimeTweenManager.Animate(tween);
        }
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, [NotNull] Action<T, UnityEngine.Vector2> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, float duration, [NotNull] Action<T, UnityEngine.Vector2> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector2 startValue, UnityEngine.Vector2 endValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Vector2> onValueChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector2>(startValue, endValue, settings), onValueChange);
        public static Tween Custom<T>([NotNull] T target, TweenSettings<UnityEngine.Vector2> settings, [NotNull] Action<T, UnityEngine.Vector2> onValueChange) where T : class 
            => Custom_internal(target, settings, onValueChange);
        #if PRIME_TWEEN_EXPERIMENTAL
        public static Tween CustomAdditive<T>([NotNull] T target, UnityEngine.Vector2 deltaValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Vector2> onDeltaChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector2>(default, deltaValue, settings), onDeltaChange, true);
        #endif
        static Tween Custom_internal<T>([NotNull] T target, TweenSettings<UnityEngine.Vector2> settings, [NotNull] Action<T, UnityEngine.Vector2> onValueChange, bool isAdditive = false) where T : class {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.isAdditive = isAdditive;
            tween.Setup(target, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<T, UnityEngine.Vector2>;
                var _target = _tween.target as T;
                UnityEngine.Vector2 val;
                if (_tween.isAdditive) {
                    var newVal = _tween.Vector2Val;
                    val = newVal.calcDelta(_tween.prevVal);
                    _tween.prevVal.Vector2Val = newVal;
                } else {
                    val = _tween.Vector2Val;
                }
                try {
                    _onValueChange(_target, val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e, _target as UnityEngine.Object);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomVector2);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animate(object target, ref TweenSettings<UnityEngine.Vector2> settings, [NotNull] Action<ReusableTween> setter, Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animateWithIntParam([NotNull] object target, int intParam, ref TweenSettings<UnityEngine.Vector2> settings, [NotNull] Action<ReusableTween> setter, [NotNull] Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.intParam = intParam;
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }

        public static Tween Custom(UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, [NotNull] Action<UnityEngine.Vector3> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, [NotNull] Action<UnityEngine.Vector3> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings, [NotNull] Action<UnityEngine.Vector3> onValueChange) => Custom(new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings), onValueChange);
        public static Tween Custom(TweenSettings<UnityEngine.Vector3> settings, [NotNull] Action<UnityEngine.Vector3> onValueChange) {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.Setup(PrimeTweenManager.dummyTarget, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<UnityEngine.Vector3>;
                var val = _tween.Vector3Val;
                try {
                    _onValueChange(val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomVector3);
            return PrimeTweenManager.Animate(tween);
        }
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, [NotNull] Action<T, UnityEngine.Vector3> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float duration, [NotNull] Action<T, UnityEngine.Vector3> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Vector3> onValueChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, settings), onValueChange);
        public static Tween Custom<T>([NotNull] T target, TweenSettings<UnityEngine.Vector3> settings, [NotNull] Action<T, UnityEngine.Vector3> onValueChange) where T : class 
            => Custom_internal(target, settings, onValueChange);
        #if PRIME_TWEEN_EXPERIMENTAL
        public static Tween CustomAdditive<T>([NotNull] T target, UnityEngine.Vector3 deltaValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Vector3> onDeltaChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector3>(default, deltaValue, settings), onDeltaChange, true);
        #endif
        static Tween Custom_internal<T>([NotNull] T target, TweenSettings<UnityEngine.Vector3> settings, [NotNull] Action<T, UnityEngine.Vector3> onValueChange, bool isAdditive = false) where T : class {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.isAdditive = isAdditive;
            tween.Setup(target, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<T, UnityEngine.Vector3>;
                var _target = _tween.target as T;
                UnityEngine.Vector3 val;
                if (_tween.isAdditive) {
                    var newVal = _tween.Vector3Val;
                    val = newVal.calcDelta(_tween.prevVal);
                    _tween.prevVal.Vector3Val = newVal;
                } else {
                    val = _tween.Vector3Val;
                }
                try {
                    _onValueChange(_target, val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e, _target as UnityEngine.Object);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomVector3);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animate(object target, ref TweenSettings<UnityEngine.Vector3> settings, [NotNull] Action<ReusableTween> setter, Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animateWithIntParam([NotNull] object target, int intParam, ref TweenSettings<UnityEngine.Vector3> settings, [NotNull] Action<ReusableTween> setter, [NotNull] Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.intParam = intParam;
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }

        public static Tween Custom(UnityEngine.Vector4 startValue, UnityEngine.Vector4 endValue, float duration, [NotNull] Action<UnityEngine.Vector4> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Vector4>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Vector4 startValue, UnityEngine.Vector4 endValue, float duration, [NotNull] Action<UnityEngine.Vector4> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Vector4>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Vector4 startValue, UnityEngine.Vector4 endValue, TweenSettings settings, [NotNull] Action<UnityEngine.Vector4> onValueChange) => Custom(new TweenSettings<UnityEngine.Vector4>(startValue, endValue, settings), onValueChange);
        public static Tween Custom(TweenSettings<UnityEngine.Vector4> settings, [NotNull] Action<UnityEngine.Vector4> onValueChange) {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.Setup(PrimeTweenManager.dummyTarget, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<UnityEngine.Vector4>;
                var val = _tween.Vector4Val;
                try {
                    _onValueChange(val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomVector4);
            return PrimeTweenManager.Animate(tween);
        }
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector4 startValue, UnityEngine.Vector4 endValue, float duration, [NotNull] Action<T, UnityEngine.Vector4> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector4>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector4 startValue, UnityEngine.Vector4 endValue, float duration, [NotNull] Action<T, UnityEngine.Vector4> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector4>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Vector4 startValue, UnityEngine.Vector4 endValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Vector4> onValueChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector4>(startValue, endValue, settings), onValueChange);
        public static Tween Custom<T>([NotNull] T target, TweenSettings<UnityEngine.Vector4> settings, [NotNull] Action<T, UnityEngine.Vector4> onValueChange) where T : class 
            => Custom_internal(target, settings, onValueChange);
        #if PRIME_TWEEN_EXPERIMENTAL
        public static Tween CustomAdditive<T>([NotNull] T target, UnityEngine.Vector4 deltaValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Vector4> onDeltaChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Vector4>(default, deltaValue, settings), onDeltaChange, true);
        #endif
        static Tween Custom_internal<T>([NotNull] T target, TweenSettings<UnityEngine.Vector4> settings, [NotNull] Action<T, UnityEngine.Vector4> onValueChange, bool isAdditive = false) where T : class {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.isAdditive = isAdditive;
            tween.Setup(target, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<T, UnityEngine.Vector4>;
                var _target = _tween.target as T;
                UnityEngine.Vector4 val;
                if (_tween.isAdditive) {
                    var newVal = _tween.Vector4Val;
                    val = newVal.calcDelta(_tween.prevVal);
                    _tween.prevVal.Vector4Val = newVal;
                } else {
                    val = _tween.Vector4Val;
                }
                try {
                    _onValueChange(_target, val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e, _target as UnityEngine.Object);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomVector4);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animate(object target, ref TweenSettings<UnityEngine.Vector4> settings, [NotNull] Action<ReusableTween> setter, Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animateWithIntParam([NotNull] object target, int intParam, ref TweenSettings<UnityEngine.Vector4> settings, [NotNull] Action<ReusableTween> setter, [NotNull] Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.intParam = intParam;
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }

        public static Tween Custom(UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, [NotNull] Action<UnityEngine.Quaternion> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, [NotNull] Action<UnityEngine.Quaternion> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, TweenSettings settings, [NotNull] Action<UnityEngine.Quaternion> onValueChange) => Custom(new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, settings), onValueChange);
        public static Tween Custom(TweenSettings<UnityEngine.Quaternion> settings, [NotNull] Action<UnityEngine.Quaternion> onValueChange) {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.Setup(PrimeTweenManager.dummyTarget, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<UnityEngine.Quaternion>;
                var val = _tween.QuaternionVal;
                try {
                    _onValueChange(val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomQuaternion);
            return PrimeTweenManager.Animate(tween);
        }
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, [NotNull] Action<T, UnityEngine.Quaternion> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float duration, [NotNull] Action<T, UnityEngine.Quaternion> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Quaternion> onValueChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, settings), onValueChange);
        public static Tween Custom<T>([NotNull] T target, TweenSettings<UnityEngine.Quaternion> settings, [NotNull] Action<T, UnityEngine.Quaternion> onValueChange) where T : class 
            => Custom_internal(target, settings, onValueChange);
        #if PRIME_TWEEN_EXPERIMENTAL
        public static Tween CustomAdditive<T>([NotNull] T target, UnityEngine.Quaternion deltaValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Quaternion> onDeltaChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Quaternion>(default, deltaValue, settings), onDeltaChange, true);
        #endif
        static Tween Custom_internal<T>([NotNull] T target, TweenSettings<UnityEngine.Quaternion> settings, [NotNull] Action<T, UnityEngine.Quaternion> onValueChange, bool isAdditive = false) where T : class {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.isAdditive = isAdditive;
            tween.Setup(target, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<T, UnityEngine.Quaternion>;
                var _target = _tween.target as T;
                UnityEngine.Quaternion val;
                if (_tween.isAdditive) {
                    var newVal = _tween.QuaternionVal;
                    val = newVal.calcDelta(_tween.prevVal);
                    _tween.prevVal.QuaternionVal = newVal;
                } else {
                    val = _tween.QuaternionVal;
                }
                try {
                    _onValueChange(_target, val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e, _target as UnityEngine.Object);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomQuaternion);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animate(object target, ref TweenSettings<UnityEngine.Quaternion> settings, [NotNull] Action<ReusableTween> setter, Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animateWithIntParam([NotNull] object target, int intParam, ref TweenSettings<UnityEngine.Quaternion> settings, [NotNull] Action<ReusableTween> setter, [NotNull] Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.intParam = intParam;
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }

        public static Tween Custom(UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, [NotNull] Action<UnityEngine.Rect> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, [NotNull] Action<UnityEngine.Rect> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => Custom(new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom(UnityEngine.Rect startValue, UnityEngine.Rect endValue, TweenSettings settings, [NotNull] Action<UnityEngine.Rect> onValueChange) => Custom(new TweenSettings<UnityEngine.Rect>(startValue, endValue, settings), onValueChange);
        public static Tween Custom(TweenSettings<UnityEngine.Rect> settings, [NotNull] Action<UnityEngine.Rect> onValueChange) {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.Setup(PrimeTweenManager.dummyTarget, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<UnityEngine.Rect>;
                var val = _tween.RectVal;
                try {
                    _onValueChange(val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomRect);
            return PrimeTweenManager.Animate(tween);
        }
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, [NotNull] Action<T, UnityEngine.Rect> onValueChange, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, float duration, [NotNull] Action<T, UnityEngine.Rect> onValueChange, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)), onValueChange);
        public static Tween Custom<T>([NotNull] T target, UnityEngine.Rect startValue, UnityEngine.Rect endValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Rect> onValueChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Rect>(startValue, endValue, settings), onValueChange);
        public static Tween Custom<T>([NotNull] T target, TweenSettings<UnityEngine.Rect> settings, [NotNull] Action<T, UnityEngine.Rect> onValueChange) where T : class 
            => Custom_internal(target, settings, onValueChange);
        #if PRIME_TWEEN_EXPERIMENTAL
        public static Tween CustomAdditive<T>([NotNull] T target, UnityEngine.Rect deltaValue, TweenSettings settings, [NotNull] Action<T, UnityEngine.Rect> onDeltaChange) where T : class 
            => Custom_internal(target, new TweenSettings<UnityEngine.Rect>(default, deltaValue, settings), onDeltaChange, true);
        #endif
        static Tween Custom_internal<T>([NotNull] T target, TweenSettings<UnityEngine.Rect> settings, [NotNull] Action<T, UnityEngine.Rect> onValueChange, bool isAdditive = false) where T : class {
            Assert.IsNotNull(onValueChange);
            if (settings.startFromCurrent) {
                UnityEngine.Debug.LogWarning(Constants.customTweensDontSupportStartFromCurrentWarning);
            }
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.customOnValueChange = onValueChange;
            tween.isAdditive = isAdditive;
            tween.Setup(target, ref settings.settings, _tween => {
                var _onValueChange = _tween.customOnValueChange as Action<T, UnityEngine.Rect>;
                var _target = _tween.target as T;
                UnityEngine.Rect val;
                if (_tween.isAdditive) {
                    var newVal = _tween.RectVal;
                    val = newVal.calcDelta(_tween.prevVal);
                    _tween.prevVal.RectVal = newVal;
                } else {
                    val = _tween.RectVal;
                }
                try {
                    _onValueChange(_target, val);
                } catch (Exception e) {
                    UnityEngine.Debug.LogException(e, _target as UnityEngine.Object);
                    Assert.LogWarning($"Tween was stopped because of exception in {nameof(onValueChange)} callback, tween: {_tween.GetDescription()}\n", _tween.id, _tween.target as UnityEngine.Object);
                    _tween.EmergencyStop();
                }
            }, null, false, TweenType.CustomRect);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animate(object target, ref TweenSettings<UnityEngine.Rect> settings, [NotNull] Action<ReusableTween> setter, Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
        static Tween animateWithIntParam([NotNull] object target, int intParam, ref TweenSettings<UnityEngine.Rect> settings, [NotNull] Action<ReusableTween> setter, [NotNull] Func<ReusableTween, ValueContainer> getter, TweenType _tweenType) {
            var tween = PrimeTweenManager.fetchTween();
            tween.intParam = intParam;
            tween.startValue.CopyFrom(ref settings.startValue);
            tween.endValue.CopyFrom(ref settings.endValue);
            tween.Setup(target, ref settings.settings, setter, getter, settings.startFromCurrent, _tweenType);
            return PrimeTweenManager.Animate(tween);
        }
#if PRIME_TWEEN_EXPERIMENTAL        
        public static Tween PositionAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween PositionAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween PositionAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, TweenSettings settings) 
            => CustomAdditive(target, deltaValue, settings, (_target, delta) => _target.position += delta);
        
        public static Tween LocalPositionAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween LocalPositionAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween LocalPositionAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, TweenSettings settings) 
            => CustomAdditive(target, deltaValue, settings, (_target, delta) => _target.localPosition += delta);
        
        public static Tween ScaleAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween ScaleAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => ScaleAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween ScaleAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, TweenSettings settings) 
            => CustomAdditive(target, deltaValue, settings, (_target, delta) => _target.localScale += delta);
        
        public static Tween RotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween RotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween RotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, TweenSettings settings) 
            => CustomAdditive(target, deltaValue, settings, (_target, delta) => _target.rotation *= UnityEngine.Quaternion.Euler(delta));
        
        public static Tween LocalRotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween LocalRotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween LocalRotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 deltaValue, TweenSettings settings) 
            => CustomAdditive(target, deltaValue, settings, (_target, delta) => _target.localRotation *= UnityEngine.Quaternion.Euler(delta));
        
        public static Tween RotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion deltaValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween RotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion deltaValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween RotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion deltaValue, TweenSettings settings) 
            => CustomAdditive(target, deltaValue, settings, (_target, delta) => _target.rotation *= delta);
        
        public static Tween LocalRotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion deltaValue, float duration, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween LocalRotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion deltaValue, float duration, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAdditive(target, deltaValue, new TweenSettings(duration, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime));
        public static Tween LocalRotationAdditive([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion deltaValue, TweenSettings settings) 
            => CustomAdditive(target, deltaValue, settings, (_target, delta) => _target.localRotation *= delta);
#endif
        public static Tween PositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float averageSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float averageSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float averageSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween PositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float averageSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => PositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        static Tween PositionAtSpeed([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Vector3> settings) {
            var speed = settings.settings.duration;
            if (speed <= 0) {
                UnityEngine.Debug.LogError($"Invalid speed provided to the Tween.{nameof(PositionAtSpeed)}() method: {speed}.");
                return default;
            }
            if (settings.startFromCurrent) {
                settings.startFromCurrent = false;
                settings.startValue = target.position;
            }
            settings.settings.duration = Extensions.CalcDistance(settings.startValue, settings.endValue) / speed;
            return Position(target, settings);
        }

        public static Tween LocalPositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float averageSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 endValue, float averageSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float averageSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalPositionAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Vector3 startValue, UnityEngine.Vector3 endValue, float averageSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalPositionAtSpeed(target, new TweenSettings<UnityEngine.Vector3>(startValue, endValue, new TweenSettings(averageSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        static Tween LocalPositionAtSpeed([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Vector3> settings) {
            var speed = settings.settings.duration;
            if (speed <= 0) {
                UnityEngine.Debug.LogError($"Invalid speed provided to the Tween.{nameof(LocalPositionAtSpeed)}() method: {speed}.");
                return default;
            }
            if (settings.startFromCurrent) {
                settings.startFromCurrent = false;
                settings.startValue = target.localPosition;
            }
            settings.settings.duration = Extensions.CalcDistance(settings.startValue, settings.endValue) / speed;
            return LocalPosition(target, settings);
        }

        public static Tween RotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float averageAngularSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float averageAngularSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float averageAngularSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween RotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float averageAngularSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => RotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        static Tween RotationAtSpeed([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Quaternion> settings) {
            var speed = settings.settings.duration;
            if (speed <= 0) {
                UnityEngine.Debug.LogError($"Invalid speed provided to the Tween.{nameof(RotationAtSpeed)}() method: {speed}.");
                return default;
            }
            if (settings.startFromCurrent) {
                settings.startFromCurrent = false;
                settings.startValue = target.rotation;
            }
            settings.settings.duration = Extensions.CalcDistance(settings.startValue, settings.endValue) / speed;
            return Rotation(target, settings);
        }

        public static Tween LocalRotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float averageAngularSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion endValue, float averageAngularSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float averageAngularSpeed, Ease ease = Ease.Default, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        public static Tween LocalRotationAtSpeed([NotNull] UnityEngine.Transform target, UnityEngine.Quaternion startValue, UnityEngine.Quaternion endValue, float averageAngularSpeed, Easing ease, int cycles = 1, CycleMode cycleMode = CycleMode.Restart, float startDelay = 0, float endDelay = 0, bool useUnscaledTime = false) 
            => LocalRotationAtSpeed(target, new TweenSettings<UnityEngine.Quaternion>(startValue, endValue, new TweenSettings(averageAngularSpeed, ease, cycles, cycleMode, startDelay, endDelay, useUnscaledTime)));
        static Tween LocalRotationAtSpeed([NotNull] UnityEngine.Transform target, TweenSettings<UnityEngine.Quaternion> settings) {
            var speed = settings.settings.duration;
            if (speed <= 0) {
                UnityEngine.Debug.LogError($"Invalid speed provided to the Tween.{nameof(LocalRotationAtSpeed)}() method: {speed}.");
                return default;
            }
            if (settings.startFromCurrent) {
                settings.startFromCurrent = false;
                settings.startValue = target.localRotation;
            }
            settings.settings.duration = Extensions.CalcDistance(settings.startValue, settings.endValue) / speed;
            return LocalRotation(target, settings);
        }

    }
}