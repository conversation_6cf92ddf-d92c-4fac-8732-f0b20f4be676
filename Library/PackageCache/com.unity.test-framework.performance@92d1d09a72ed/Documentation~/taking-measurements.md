# Taking measurements

The Performance Testing Package provides several API methods for taking measurements in your performance test, depending on what you need to measure and how you want to do it. Include `using Unity.PerformanceTesting` at the top of your script to access the methods.

The pages in this section detail the specifics of each measurement method with examples:

* [Measure.Method](./measure-method.md)
* [Measure.Frames](./measure-frames.md)
* [Measure.Scope](./measure-scope.md)
* [Measure.ProfilerMarkers](./measure-profile-markers.md)
* [Measure.Custom](./measure-custom.md)

